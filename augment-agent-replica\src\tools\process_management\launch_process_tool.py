"""
Launch Process Tool - Exact replica of Augment Agent's launch-process functionality
Command execution with wait/background modes and timeout management
"""

import os
from typing import Dict, Any
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult
from .process_manager import process_manager

class LaunchProcessTool(BaseTool):
    """
    Launch a new process with a shell command
    Exact replica of original Augment Agent launch-process tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.PROCESS_MANAGEMENT
    
    def _get_description(self) -> str:
        return """Launch a new process with a shell command. A process can be waiting (`wait=true`) or non-waiting (`wait=false`).

If `wait=true`, launches the process in an interactive terminal, and waits for the process to complete up to
`max_wait_seconds` seconds. If the process ends during this period, the tool call returns. If the timeout
expires, the process will continue running in the background but the tool call will return. You can then
interact with the process using the other process tools.

Note: Only one waiting process can be running at a time. If you try to launch a process with `wait=true`
while another is running, the tool will return an error.

If `wait=false`, launches a background process in a separate terminal. This returns immediately, while the
process keeps running in the background.

Notes:
- Use `wait=true` processes when the command is expected to be short, or when you can't
proceed with your task until the process is complete. Use `wait=false` for processes that are
expected to run in the background, such as starting a server you'll need to interact with, or a
long-running process that does not need to complete before proceeding with the task.
- If this tool returns while the process is still running, you can continue to interact with the process
using the other available tools. You can wait for the process, read from it, write to it, kill it, etc.
- You can use this tool to interact with the user's local version control system. Do not use the
retrieval tool for that purpose.
- If there is a more specific tool available that can perform the function, use that tool instead of
this one.

The OS is win32. The shell is 'bash'."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "command": {
                    "description": "The shell command to execute.",
                    "type": "string"
                },
                "wait": {
                    "description": "Whether to wait for the command to complete.",
                    "type": "boolean"
                },
                "max_wait_seconds": {
                    "description": "Number of seconds to wait for the command to complete. Only relevant when wait=true. 10 minutes may be a good default: increase from there if needed.",
                    "type": "number"
                },
                "cwd": {
                    "description": "Required parameter. Absolute path to the working directory for the command.",
                    "type": "string"
                }
            },
            "required": ["command", "wait", "max_wait_seconds", "cwd"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the launch-process tool"""
        try:
            command = kwargs.get('command', '')
            wait = kwargs.get('wait', False)
            max_wait_seconds = kwargs.get('max_wait_seconds', 600)
            cwd = kwargs.get('cwd', '')
            
            # Validate parameters
            if not command:
                return ToolResult(
                    success=False,
                    error="command parameter is required"
                )
            
            if not cwd:
                return ToolResult(
                    success=False,
                    error="cwd parameter is required"
                )
            
            # Validate working directory
            if not os.path.exists(cwd):
                return ToolResult(
                    success=False,
                    error=f"Working directory does not exist: {cwd}"
                )
            
            if not os.path.isdir(cwd):
                return ToolResult(
                    success=False,
                    error=f"Working directory is not a directory: {cwd}"
                )
            
            # Check if there's already a waiting process
            if wait:
                processes = process_manager.list_processes()
                waiting_processes = [p for p in processes if p['wait_mode'] and p['state'] == 'running']
                if waiting_processes:
                    return ToolResult(
                        success=False,
                        error=f"Another waiting process is already running (terminal {waiting_processes[0]['terminal_id']}). Only one waiting process can run at a time."
                    )
            
            # Launch the process
            terminal_id = process_manager.launch_process(
                command=command,
                cwd=cwd,
                wait=wait,
                max_wait_seconds=max_wait_seconds
            )
            
            # Get process result
            result = process_manager.read_process(terminal_id, wait=False)
            
            if wait:
                # For waiting processes, include the output in the response
                return ToolResult(
                    success=True,
                    data={
                        'terminal_id': terminal_id,
                        'command': command,
                        'cwd': cwd,
                        'wait_mode': wait,
                        'max_wait_seconds': max_wait_seconds,
                        'state': result['state'],
                        'return_code': result['return_code'],
                        'stdout': result['stdout'],
                        'stderr': result['stderr'],
                        'pid': result['pid'],
                        'execution_time': result.get('end_time', 0) - result.get('start_time', 0) if result.get('end_time') else None
                    }
                )
            else:
                # For non-waiting processes, just return the terminal info
                return ToolResult(
                    success=True,
                    data={
                        'terminal_id': terminal_id,
                        'command': command,
                        'cwd': cwd,
                        'wait_mode': wait,
                        'state': result['state'],
                        'pid': result['pid'],
                        'message': f"Process launched in background. Use terminal_id {terminal_id} to interact with it."
                    }
                )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error launching process: {str(e)}"
            )
