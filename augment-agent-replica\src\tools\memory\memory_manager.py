"""
Memory Manager - Central management for long-term memory storage
"""

import sqlite3
import time
import json
import hashlib
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class Memory:
    """Represents a stored memory"""
    memory_id: str
    content: str
    context: Dict[str, Any]
    created_at: float
    conversation_id: str
    tags: List[str]
    importance: float = 1.0  # 0.0 to 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert memory to dictionary"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Memory':
        """Create memory from dictionary"""
        return cls(**data)

class MemoryManager:
    """Central manager for long-term memory storage"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or ":memory:"
        self.current_conversation_id = "default"
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database for memory storage"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS memories (
                    memory_id TEXT PRIMARY KEY,
                    content TEXT NOT NULL,
                    context TEXT,
                    created_at REAL,
                    conversation_id TEXT,
                    tags TEXT,
                    importance REAL DEFAULT 1.0
                )
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_memories_conversation 
                ON memories(conversation_id)
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_memories_created 
                ON memories(created_at)
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_memories_importance 
                ON memories(importance)
            ''')
            
            # Full-text search index
            conn.execute('''
                CREATE VIRTUAL TABLE IF NOT EXISTS memories_fts USING fts5(
                    memory_id,
                    content,
                    tags,
                    content='memories',
                    content_rowid='rowid'
                )
            ''')
            
            # Triggers to keep FTS in sync
            conn.execute('''
                CREATE TRIGGER IF NOT EXISTS memories_ai AFTER INSERT ON memories BEGIN
                    INSERT INTO memories_fts(memory_id, content, tags) 
                    VALUES (new.memory_id, new.content, new.tags);
                END
            ''')
            
            conn.execute('''
                CREATE TRIGGER IF NOT EXISTS memories_ad AFTER DELETE ON memories BEGIN
                    DELETE FROM memories_fts WHERE memory_id = old.memory_id;
                END
            ''')
            
            conn.execute('''
                CREATE TRIGGER IF NOT EXISTS memories_au AFTER UPDATE ON memories BEGIN
                    UPDATE memories_fts SET content = new.content, tags = new.tags 
                    WHERE memory_id = new.memory_id;
                END
            ''')
    
    def set_conversation_id(self, conversation_id: str):
        """Set the current conversation ID"""
        self.current_conversation_id = conversation_id
    
    def store_memory(
        self,
        content: str,
        context: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        importance: float = 1.0
    ) -> str:
        """Store a new memory"""
        # Generate memory ID based on content hash and timestamp
        content_hash = hashlib.md5(content.encode()).hexdigest()[:8]
        timestamp = str(int(time.time()))
        memory_id = f"mem_{timestamp}_{content_hash}"
        
        memory = Memory(
            memory_id=memory_id,
            content=content,
            context=context or {},
            created_at=time.time(),
            conversation_id=self.current_conversation_id,
            tags=tags or [],
            importance=max(0.0, min(1.0, importance))  # Clamp to 0-1
        )
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT INTO memories 
                    (memory_id, content, context, created_at, conversation_id, tags, importance)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    memory.memory_id,
                    memory.content,
                    json.dumps(memory.context),
                    memory.created_at,
                    memory.conversation_id,
                    json.dumps(memory.tags),
                    memory.importance
                ))
            
            logger.info(f"Stored memory: {memory_id}")
            return memory_id
        
        except Exception as e:
            logger.error(f"Error storing memory: {e}")
            raise
    
    def get_memory(self, memory_id: str) -> Optional[Memory]:
        """Get a specific memory by ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    'SELECT * FROM memories WHERE memory_id = ?',
                    (memory_id,)
                )
                row = cursor.fetchone()
                
                if row:
                    data = dict(row)
                    data['context'] = json.loads(data['context'])
                    data['tags'] = json.loads(data['tags'])
                    return Memory.from_dict(data)
        
        except Exception as e:
            logger.error(f"Error getting memory {memory_id}: {e}")
        
        return None
    
    def search_memories(
        self,
        query: str,
        conversation_id: Optional[str] = None,
        limit: int = 10,
        min_importance: float = 0.0
    ) -> List[Memory]:
        """Search memories using full-text search"""
        memories = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                # Use conversation_id if provided, otherwise use current
                search_conversation_id = conversation_id or self.current_conversation_id
                
                # Full-text search query
                cursor = conn.execute('''
                    SELECT m.* FROM memories m
                    JOIN memories_fts fts ON m.memory_id = fts.memory_id
                    WHERE fts MATCH ? 
                    AND m.conversation_id = ?
                    AND m.importance >= ?
                    ORDER BY m.importance DESC, m.created_at DESC
                    LIMIT ?
                ''', (query, search_conversation_id, min_importance, limit))
                
                for row in cursor.fetchall():
                    data = dict(row)
                    data['context'] = json.loads(data['context'])
                    data['tags'] = json.loads(data['tags'])
                    memories.append(Memory.from_dict(data))
        
        except Exception as e:
            logger.error(f"Error searching memories: {e}")
        
        return memories
    
    def get_recent_memories(
        self,
        limit: int = 10,
        conversation_id: Optional[str] = None,
        min_importance: float = 0.0
    ) -> List[Memory]:
        """Get recent memories"""
        memories = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                search_conversation_id = conversation_id or self.current_conversation_id
                
                cursor = conn.execute('''
                    SELECT * FROM memories 
                    WHERE conversation_id = ? AND importance >= ?
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (search_conversation_id, min_importance, limit))
                
                for row in cursor.fetchall():
                    data = dict(row)
                    data['context'] = json.loads(data['context'])
                    data['tags'] = json.loads(data['tags'])
                    memories.append(Memory.from_dict(data))
        
        except Exception as e:
            logger.error(f"Error getting recent memories: {e}")
        
        return memories
    
    def get_memories_by_tags(
        self,
        tags: List[str],
        conversation_id: Optional[str] = None,
        limit: int = 10
    ) -> List[Memory]:
        """Get memories that have any of the specified tags"""
        memories = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                search_conversation_id = conversation_id or self.current_conversation_id
                
                # Create a query that matches any of the tags
                tag_conditions = []
                params = [search_conversation_id]
                
                for tag in tags:
                    tag_conditions.append('tags LIKE ?')
                    params.append(f'%"{tag}"%')
                
                params.append(limit)
                
                query = f'''
                    SELECT * FROM memories 
                    WHERE conversation_id = ? AND ({' OR '.join(tag_conditions)})
                    ORDER BY importance DESC, created_at DESC
                    LIMIT ?
                '''
                
                cursor = conn.execute(query, params)
                
                for row in cursor.fetchall():
                    data = dict(row)
                    data['context'] = json.loads(data['context'])
                    data['tags'] = json.loads(data['tags'])
                    memories.append(Memory.from_dict(data))
        
        except Exception as e:
            logger.error(f"Error getting memories by tags: {e}")
        
        return memories
    
    def delete_memory(self, memory_id: str) -> bool:
        """Delete a memory"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'DELETE FROM memories WHERE memory_id = ?',
                    (memory_id,)
                )
                return cursor.rowcount > 0
        
        except Exception as e:
            logger.error(f"Error deleting memory {memory_id}: {e}")
            return False
    
    def cleanup_old_memories(self, max_age_days: int = 365, min_importance: float = 0.1):
        """Clean up old, low-importance memories"""
        try:
            cutoff_time = time.time() - (max_age_days * 24 * 3600)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    DELETE FROM memories 
                    WHERE created_at < ? AND importance < ?
                ''', (cutoff_time, min_importance))
                
                deleted_count = cursor.rowcount
                logger.info(f"Cleaned up {deleted_count} old memories")
                return deleted_count
        
        except Exception as e:
            logger.error(f"Error cleaning up memories: {e}")
            return 0
    
    def get_memory_stats(self, conversation_id: Optional[str] = None) -> Dict[str, Any]:
        """Get statistics about stored memories"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                search_conversation_id = conversation_id or self.current_conversation_id
                
                cursor = conn.execute('''
                    SELECT 
                        COUNT(*) as total_memories,
                        AVG(importance) as avg_importance,
                        MIN(created_at) as oldest_memory,
                        MAX(created_at) as newest_memory
                    FROM memories 
                    WHERE conversation_id = ?
                ''', (search_conversation_id,))
                
                row = cursor.fetchone()
                
                return {
                    'total_memories': row[0],
                    'average_importance': row[1] or 0.0,
                    'oldest_memory': row[2],
                    'newest_memory': row[3],
                    'conversation_id': search_conversation_id
                }
        
        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            return {
                'total_memories': 0,
                'average_importance': 0.0,
                'oldest_memory': None,
                'newest_memory': None,
                'conversation_id': conversation_id or self.current_conversation_id
            }

# Global memory manager instance
memory_manager = MemoryManager()
