import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { spawn, ChildProcess } from 'child_process';
import axios from 'axios';
import WebSocket from 'ws';

// ===== COMPLETE TYPE DEFINITIONS FOR ALL 25+ TOOLS =====

export interface AgentContext {
    workspaceRoot: string;
    activeFile?: string;
    selection?: string;
    conversationId?: string;
    timestamp?: string;
    userMessage?: string;
}

export interface StreamChunk {
    type: 'content' | 'tool_result' | 'error' | 'thinking' | 'complete';
    content?: string;
    tool_name?: string;
    data?: any;
    metadata?: any;
}

// File Management Tool Interfaces
export interface ViewParams {
    path: string;
    type: 'file' | 'directory';
    view_range?: [number, number];
    search_query_regex?: string;
    case_sensitive?: boolean;
    context_lines_before?: number;
    context_lines_after?: number;
}

export interface StrReplaceEditorParams {
    command: 'str_replace' | 'insert';
    path: string;
    instruction_reminder: string;
    old_str_1?: string;
    new_str_1?: string;
    old_str_start_line_number_1?: number;
    old_str_end_line_number_1?: number;
    insert_line_1?: number;
    // Support for multiple replacements
    old_str_2?: string;
    new_str_2?: string;
    old_str_start_line_number_2?: number;
    old_str_end_line_number_2?: number;
}

export interface SaveFileParams {
    instructions_reminder: string;
    path: string;
    file_content: string;
    add_last_line_newline?: boolean;
}

export interface RemoveFilesParams {
    file_paths: string[];
}

// Code Intelligence Tool Interfaces
export interface CodebaseRetrievalParams {
    information_request: string;
}

export interface DiagnosticsParams {
    paths: string[];
}

// Process Management Tool Interfaces
export interface LaunchProcessParams {
    command: string;
    wait: boolean;
    max_wait_seconds: number;
    cwd: string;
}

export interface ReadProcessParams {
    terminal_id: number;
    wait: boolean;
    max_wait_seconds: number;
}

export interface WriteProcessParams {
    terminal_id: number;
    input_text: string;
}

export interface KillProcessParams {
    terminal_id: number;
}

export interface ListProcessesParams {
    // No parameters required
}

export interface ReadTerminalParams {
    only_selected?: boolean;
}

// Web Integration Tool Interfaces
export interface WebSearchParams {
    query: string;
    num_results?: number;
}

export interface WebFetchParams {
    url: string;
}

export interface OpenBrowserParams {
    url: string;
}

// Task Management Tool Interfaces
export interface ViewTasklistParams {
    // No parameters required
}

export interface AddTasksParams {
    // Single task mode
    name?: string;
    description?: string;
    parent_task_id?: string;
    after_task_id?: string;
    state?: 'NOT_STARTED' | 'IN_PROGRESS' | 'CANCELLED' | 'COMPLETE';
    // Multiple tasks mode
    tasks?: Array<{
        name: string;
        description: string;
        parent_task_id?: string;
        after_task_id?: string;
        state?: 'NOT_STARTED' | 'IN_PROGRESS' | 'CANCELLED' | 'COMPLETE';
    }>;
}

export interface UpdateTasksParams {
    // Single task mode
    task_id?: string;
    name?: string;
    description?: string;
    state?: 'NOT_STARTED' | 'IN_PROGRESS' | 'CANCELLED' | 'COMPLETE';
    // Multiple tasks mode
    tasks?: Array<{
        task_id: string;
        name?: string;
        description?: string;
        state?: 'NOT_STARTED' | 'IN_PROGRESS' | 'CANCELLED' | 'COMPLETE';
    }>;
}

export interface ReorganizeTasklistParams {
    markdown: string;
}

// Memory & Documentation Tool Interfaces
export interface RememberParams {
    memory: string;
}

export interface RenderMermaidParams {
    diagram_definition: string;
    title?: string;
}

// Content Analysis Tool Interfaces
export interface ViewRangeUntruncatedParams {
    reference_id: string;
    start_line: number;
    end_line: number;
}

export interface SearchUntruncatedParams {
    reference_id: string;
    search_term: string;
    context_lines?: number;
}

// Tool Result Interfaces
export interface ToolResult {
    success: boolean;
    data?: any;
    error?: string;
    metadata?: any;
}

export interface FileContent {
    path: string;
    content: string;
    type: 'file' | 'directory';
    size?: number;
    modified?: string;
    language?: string;
}

export interface SearchResult {
    name: string;
    type: string;
    file_path: string;
    line_number: number;
    snippet: string;
    relevance_score: number;
    context: string;
}

export interface DiagnosticItem {
    file_path: string;
    line_number: number;
    column: number;
    severity: 'error' | 'warning' | 'info' | 'hint';
    message: string;
    source: string;
    code?: string;
    rule?: string;
}

export interface ProcessInfo {
    terminal_id: number;
    command: string;
    cwd: string;
    state: string;
    pid?: number;
    return_code?: number;
    start_time?: number;
    end_time?: number;
    wait_mode: boolean;
}

export interface TaskItem {
    task_id: string;
    name: string;
    description: string;
    state: 'NOT_STARTED' | 'IN_PROGRESS' | 'CANCELLED' | 'COMPLETE';
    parent_task_id?: string;
    created_at: number;
    updated_at: number;
    completed_at?: number;
    order_index: number;
}

export interface MemoryItem {
    memory_id: string;
    content: string;
    importance: number;
    tags: string[];
    created_at: number;
    conversation_id: string;
}

// Connection and Communication Interfaces
export interface ConnectionConfig {
    serverUrl: string;
    wsUrl: string;
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
}

export class AugmentAgentClient {
    private serverProcess?: ChildProcess;
    private serverUrl = 'http://localhost:8000';
    private wsUrl = 'ws://localhost:8000/ws';
    private isServerRunning = false;
    private websocket?: WebSocket;
    private connectionConfig: ConnectionConfig;
    private messageQueue: any[] = [];
    private isConnecting = false;

    constructor() {
        this.connectionConfig = {
            serverUrl: this.serverUrl,
            wsUrl: this.wsUrl,
            timeout: 30000,
            retryAttempts: 3,
            retryDelay: 1000
        };
        this.checkServerStatus();
        this.initializeWebSocket();
    }

    // ===== CONNECTION MANAGEMENT =====

    private async initializeWebSocket(): Promise<void> {
        if (this.isConnecting || this.websocket?.readyState === WebSocket.OPEN) {
            return;
        }

        this.isConnecting = true;
        try {
            this.websocket = new WebSocket(this.wsUrl);

            this.websocket.onopen = () => {
                console.log('WebSocket connected to Augment Agent');
                this.isConnecting = false;
                this.processMessageQueue();
            };

            this.websocket.onclose = () => {
                console.log('WebSocket disconnected');
                this.isConnecting = false;
                // Auto-reconnect after delay
                setTimeout(() => this.initializeWebSocket(), this.connectionConfig.retryDelay);
            };

            this.websocket.onerror = (error: any) => {
                console.error('WebSocket error:', error);
                this.isConnecting = false;
            };

        } catch (error) {
            console.error('Failed to initialize WebSocket:', error);
            this.isConnecting = false;
        }
    }

    private processMessageQueue(): void {
        while (this.messageQueue.length > 0 && this.websocket?.readyState === WebSocket.OPEN) {
            const message = this.messageQueue.shift();
            this.websocket.send(JSON.stringify(message));
        }
    }

    // ===== FILE MANAGEMENT TOOLS (4/4) =====

    async view(params: ViewParams): Promise<ToolResult> {
        return this.executeTool('view', params);
    }

    async strReplaceEditor(params: StrReplaceEditorParams): Promise<ToolResult> {
        return this.executeTool('str-replace-editor', params);
    }

    async saveFile(params: SaveFileParams): Promise<ToolResult> {
        return this.executeTool('save-file', params);
    }

    async removeFiles(params: RemoveFilesParams): Promise<ToolResult> {
        return this.executeTool('remove-files', params);
    }

    // ===== CODE INTELLIGENCE TOOLS (2/2) =====

    async codebaseRetrieval(params: CodebaseRetrievalParams): Promise<ToolResult> {
        return this.executeTool('codebase-retrieval', params);
    }

    async diagnostics(params: DiagnosticsParams): Promise<ToolResult> {
        return this.executeTool('diagnostics', params);
    }

    // ===== PROCESS MANAGEMENT TOOLS (6/6) =====

    async launchProcess(params: LaunchProcessParams): Promise<ToolResult> {
        return this.executeTool('launch-process', params);
    }

    async readProcess(params: ReadProcessParams): Promise<ToolResult> {
        return this.executeTool('read-process', params);
    }

    async writeProcess(params: WriteProcessParams): Promise<ToolResult> {
        return this.executeTool('write-process', params);
    }

    async killProcess(params: KillProcessParams): Promise<ToolResult> {
        return this.executeTool('kill-process', params);
    }

    async listProcesses(params: ListProcessesParams = {}): Promise<ToolResult> {
        return this.executeTool('list-processes', params);
    }

    async readTerminal(params: ReadTerminalParams = {}): Promise<ToolResult> {
        return this.executeTool('read-terminal', params);
    }

    // ===== WEB INTEGRATION TOOLS (3/3) =====

    async webSearch(params: WebSearchParams): Promise<ToolResult> {
        return this.executeTool('web-search', params);
    }

    async webFetch(params: WebFetchParams): Promise<ToolResult> {
        return this.executeTool('web-fetch', params);
    }

    async openBrowser(params: OpenBrowserParams): Promise<ToolResult> {
        return this.executeTool('open-browser', params);
    }

    // ===== TASK MANAGEMENT TOOLS (4/4) =====

    async viewTasklist(params: ViewTasklistParams = {}): Promise<ToolResult> {
        return this.executeTool('view_tasklist', params);
    }

    async addTasks(params: AddTasksParams): Promise<ToolResult> {
        return this.executeTool('add_tasks', params);
    }

    async updateTasks(params: UpdateTasksParams): Promise<ToolResult> {
        return this.executeTool('update_tasks', params);
    }

    async reorganizeTasklist(params: ReorganizeTasklistParams): Promise<ToolResult> {
        return this.executeTool('reorganize_tasklist', params);
    }

    // ===== MEMORY & DOCUMENTATION TOOLS (2/2) =====

    async remember(params: RememberParams): Promise<ToolResult> {
        return this.executeTool('remember', params);
    }

    async renderMermaid(params: RenderMermaidParams): Promise<ToolResult> {
        return this.executeTool('render-mermaid', params);
    }

    // ===== CONTENT ANALYSIS TOOLS (2/2) =====

    async viewRangeUntruncated(params: ViewRangeUntruncatedParams): Promise<ToolResult> {
        return this.executeTool('view-range-untruncated', params);
    }

    async searchUntruncated(params: SearchUntruncatedParams): Promise<ToolResult> {
        return this.executeTool('search-untruncated', params);
    }

    // ===== CORE EXECUTION ENGINE =====

    private async executeTool(toolName: string, params: any): Promise<ToolResult> {
        try {
            const response = await axios.post(`${this.serverUrl}/tools/${toolName}`, {
                ...params,
                _context: this.getCurrentContext()
            }, {
                timeout: this.connectionConfig.timeout,
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            return {
                success: true,
                data: response.data.data,
                metadata: response.data.metadata
            };

        } catch (error: any) {
            return {
                success: false,
                error: error.response?.data?.error || error.message || 'Unknown error',
                metadata: { toolName, params }
            };
        }
    }

    private getCurrentContext(): AgentContext {
        const activeEditor = vscode.window.activeTextEditor;
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];

        return {
            workspaceRoot: workspaceFolder?.uri.fsPath || '',
            activeFile: activeEditor?.document.uri.fsPath,
            selection: activeEditor && !activeEditor.selection.isEmpty
                ? activeEditor.document.getText(activeEditor.selection)
                : undefined,
            conversationId: this.getConversationId(),
            timestamp: new Date().toISOString()
        };
    }

    async startServer(): Promise<boolean> {
        if (this.isServerRunning) {
            return true;
        }

        try {
            // Find the agent server script
            const extensionPath = vscode.extensions.getExtension('augment-code.augment-agent-replica')?.extensionPath;
            if (!extensionPath) {
                throw new Error('Extension path not found');
            }

            // Look for the agent server in the extension directory or workspace
            const possiblePaths = [
                path.join(extensionPath, '..', 'src', 'main.py'),
                path.join(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', 'src', 'main.py'),
                path.join(extensionPath, 'agent', 'src', 'main.py')
            ];

            let serverScript = '';
            for (const scriptPath of possiblePaths) {
                if (fs.existsSync(scriptPath)) {
                    serverScript = scriptPath;
                    break;
                }
            }

            if (!serverScript) {
                vscode.window.showErrorMessage(
                    'Augment Agent server script not found. Please ensure the agent is properly installed.',
                    'Install Agent'
                ).then(selection => {
                    if (selection === 'Install Agent') {
                        vscode.env.openExternal(vscode.Uri.parse('https://github.com/your-repo/augment-agent-replica'));
                    }
                });
                return false;
            }

            // Start the server
            this.serverProcess = spawn('python', [serverScript, '--server'], {
                cwd: path.dirname(serverScript),
                stdio: ['pipe', 'pipe', 'pipe']
            });

            // Handle server output
            this.serverProcess.stdout?.on('data', (data: any) => {
                // console.log(`Agent server: ${data}`);
            });

            this.serverProcess.stderr?.on('data', (data: any) => {
                // console.error(`Agent server error: ${data}`);
            });

            this.serverProcess.on('close', (code: any) => {
                // console.log(`Agent server exited with code ${code}`);
                this.isServerRunning = false;
            });

            // Wait for server to start
            await this.waitForServer();
            this.isServerRunning = true;
            
            vscode.window.showInformationMessage('Augment Agent server started successfully');
            return true;

        } catch (error) {
            vscode.window.showErrorMessage(`Failed to start agent server: ${error}`);
            return false;
        }
    }

    private async waitForServer(maxAttempts = 30): Promise<void> {
        for (let i = 0; i < maxAttempts; i++) {
            try {
                await axios.get(`${this.serverUrl}/health`);
                return;
            } catch (error) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        throw new Error('Server failed to start within timeout');
    }

    private async checkServerStatus(): Promise<void> {
        try {
            await axios.get(`${this.serverUrl}/health`);
            this.isServerRunning = true;
        } catch (error) {
            this.isServerRunning = false;
        }
    }

    // ===== ENHANCED STREAMING COMMUNICATION =====

    async sendMessage(message: string, context: AgentContext): Promise<AsyncGenerator<StreamChunk>> {
        if (!this.isServerRunning) {
            const started = await this.startServer();
            if (!started) {
                throw new Error('Failed to start agent server');
            }
        }

        return this.streamChat(message, context);
    }

    async sendMessageWithTools(
        message: string,
        context: AgentContext,
        availableTools?: string[]
    ): Promise<AsyncGenerator<StreamChunk>> {
        const enhancedContext = {
            ...context,
            availableTools,
            capabilities: await this.getAgentStatus()
        };

        return this.streamChat(message, enhancedContext);
    }

    async executeToolDirectly(toolName: string, params: any): Promise<ToolResult> {
        // Direct tool execution without chat interface
        return this.executeTool(toolName, params);
    }

    async batchExecuteTools(toolCalls: Array<{name: string, params: any}>): Promise<ToolResult[]> {
        const results: ToolResult[] = [];

        for (const toolCall of toolCalls) {
            try {
                const result = await this.executeTool(toolCall.name, toolCall.params);
                results.push(result);
            } catch (error: any) {
                results.push({
                    success: false,
                    error: error.message,
                    metadata: { toolName: toolCall.name, params: toolCall.params }
                });
            }
        }

        return results;
    }

    // ===== ADVANCED CONTEXT MANAGEMENT =====

    async getWorkspaceContext(): Promise<any> {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            return null;
        }

        const context = {
            workspaceRoot: workspaceFolder.uri.fsPath,
            workspaceName: workspaceFolder.name,
            openFiles: vscode.workspace.textDocuments.map((doc: any) => ({
                path: doc.uri.fsPath,
                language: doc.languageId,
                isDirty: doc.isDirty,
                lineCount: doc.lineCount
            })),
            activeFile: vscode.window.activeTextEditor?.document.uri.fsPath,
            visibleEditors: vscode.window.visibleTextEditors.map((editor: any) => ({
                path: editor.document.uri.fsPath,
                selection: editor.selection,
                viewColumn: editor.viewColumn
            })),
            gitInfo: await this.getGitInfo(workspaceFolder.uri.fsPath)
        };

        return context;
    }

    private async getGitInfo(workspacePath: string): Promise<any> {
        try {
            const gitExtension = vscode.extensions.getExtension('vscode.git')?.exports;
            if (gitExtension) {
                const repo = gitExtension.getRepository(vscode.Uri.file(workspacePath));
                if (repo) {
                    return {
                        branch: repo.state.HEAD?.name,
                        changes: repo.state.workingTreeChanges.length,
                        staged: repo.state.indexChanges.length,
                        remotes: repo.state.remotes.map((r: any) => r.name)
                    };
                }
            }
        } catch (error) {
            // Git info not available
        }
        return null;
    }

    // ===== INTELLIGENT TOOL SUGGESTIONS =====

    async suggestTools(context: string, intent: string): Promise<string[]> {
        try {
            const response = await axios.post(`${this.serverUrl}/suggest-tools`, {
                context,
                intent,
                workspace: await this.getWorkspaceContext()
            });

            return response.data.suggested_tools || [];
        } catch (error) {
            return [];
        }
    }

    async getToolDocumentation(toolName: string): Promise<any> {
        try {
            const response = await axios.get(`${this.serverUrl}/tools/${toolName}/docs`);
            return response.data;
        } catch (error) {
            return null;
        }
    }

    async validateToolParameters(toolName: string, params: any): Promise<{valid: boolean, errors?: string[]}> {
        try {
            const response = await axios.post(`${this.serverUrl}/tools/${toolName}/validate`, params);
            return response.data;
        } catch (error: any) {
            return {
                valid: false,
                errors: [error.response?.data?.error || 'Validation failed']
            };
        }
    }

    private async *streamChat(message: string, context: AgentContext): AsyncGenerator<StreamChunk> {
        try {
            const response = await axios.post(`${this.serverUrl}/chat/stream`, {
                message,
                context: {
                    workspace_root: context.workspaceRoot,
                    active_file: context.activeFile,
                    selection: context.selection,
                    conversation_id: this.getConversationId()
                }
            }, {
                responseType: 'stream',
                headers: {
                    'Accept': 'text/event-stream',
                    'Content-Type': 'application/json'
                }
            });

            let buffer = '';
            
            for await (const chunk of response.data) {
                buffer += chunk.toString();
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            yield data as StreamChunk;
                        } catch (error) {
                            // Error parsing SSE data - skip this chunk
                        }
                    }
                }
            }

        } catch (error) {
            yield {
                type: 'error',
                content: `Communication error: ${error instanceof Error ? error.message : 'Unknown error'}`
            };
        }
    }

    async executeCommand(command: string, context: AgentContext): Promise<any> {
        if (!this.isServerRunning) {
            const started = await this.startServer();
            if (!started) {
                throw new Error('Failed to start agent server');
            }
        }

        try {
            const response = await axios.post(`${this.serverUrl}/execute`, {
                command,
                context: {
                    workspace_root: context.workspaceRoot,
                    active_file: context.activeFile,
                    selection: context.selection
                }
            });

            return response.data;

        } catch (error) {
            throw new Error(`Command execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    async getAvailableTools(): Promise<any[]> {
        if (!this.isServerRunning) {
            return [];
        }

        try {
            const response = await axios.get(`${this.serverUrl}/tools`);
            return response.data.tools || [];
        } catch (error) {
            // Failed to get available tools
            return [];
        }
    }

    async getAgentStatus(): Promise<any> {
        if (!this.isServerRunning) {
            return { status: 'offline' };
        }

        try {
            const response = await axios.get(`${this.serverUrl}/status`);
            return response.data;
        } catch (error) {
            return { status: 'error', error: error instanceof Error ? error.message : 'Unknown error' };
        }
    }

    private getConversationId(): string {
        // Use workspace folder name as conversation ID
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (workspaceFolder) {
            return workspaceFolder.name;
        }
        return 'default';
    }

    // ===== ADVANCED FEATURES =====

    async getConversationHistory(conversationId?: string): Promise<any[]> {
        try {
            const id = conversationId || this.getConversationId();
            const response = await axios.get(`${this.serverUrl}/conversations/${id}/history`);
            return response.data.messages || [];
        } catch (error) {
            return [];
        }
    }

    async clearConversationHistory(conversationId?: string): Promise<boolean> {
        try {
            const id = conversationId || this.getConversationId();
            await axios.delete(`${this.serverUrl}/conversations/${id}/history`);
            return true;
        } catch (error) {
            return false;
        }
    }

    async exportConversation(conversationId?: string, format: 'json' | 'markdown' = 'json'): Promise<string | null> {
        try {
            const id = conversationId || this.getConversationId();
            const response = await axios.get(`${this.serverUrl}/conversations/${id}/export`, {
                params: { format }
            });
            return response.data.content;
        } catch (error) {
            return null;
        }
    }

    async getSystemInfo(): Promise<any> {
        try {
            const response = await axios.get(`${this.serverUrl}/system/info`);
            return response.data;
        } catch (error) {
            return {
                status: 'error',
                error: 'Failed to get system info'
            };
        }
    }

    async updateConfiguration(config: any): Promise<boolean> {
        try {
            await axios.post(`${this.serverUrl}/config/update`, config);
            return true;
        } catch (error) {
            return false;
        }
    }

    async getMetrics(): Promise<any> {
        try {
            const response = await axios.get(`${this.serverUrl}/metrics`);
            return response.data;
        } catch (error) {
            return null;
        }
    }

    // ===== WORKSPACE INTEGRATION =====

    async indexWorkspace(force: boolean = false): Promise<boolean> {
        try {
            const context = await this.getWorkspaceContext();
            await axios.post(`${this.serverUrl}/workspace/index`, {
                workspace: context,
                force
            });
            return true;
        } catch (error) {
            return false;
        }
    }

    async getWorkspaceStats(): Promise<any> {
        try {
            const response = await axios.get(`${this.serverUrl}/workspace/stats`);
            return response.data;
        } catch (error) {
            return null;
        }
    }

    async searchWorkspace(query: string, options: any = {}): Promise<any[]> {
        try {
            const response = await axios.post(`${this.serverUrl}/workspace/search`, {
                query,
                options,
                workspace: await this.getWorkspaceContext()
            });
            return response.data.results || [];
        } catch (error) {
            return [];
        }
    }

    // ===== REAL-TIME FEATURES =====

    onToolExecution(callback: (toolName: string, params: any, result: ToolResult) => void): void {
        if (this.websocket) {
            this.websocket.on('message', (data: any) => {
                try {
                    const message = JSON.parse(data.toString());
                    if (message.type === 'tool_execution') {
                        callback(message.tool_name, message.params, message.result);
                    }
                } catch (error) {
                    // Invalid message format
                }
            });
        }
    }

    onAgentThinking(callback: (thinking: string) => void): void {
        if (this.websocket) {
            this.websocket.on('message', (data: any) => {
                try {
                    const message = JSON.parse(data.toString());
                    if (message.type === 'thinking') {
                        callback(message.content);
                    }
                } catch (error) {
                    // Invalid message format
                }
            });
        }
    }

    onWorkspaceChange(callback: (change: any) => void): void {
        // Listen for workspace file changes
        const watcher = vscode.workspace.createFileSystemWatcher('**/*');

        watcher.onDidChange((uri: any) => {
            callback({ type: 'change', path: uri.fsPath });
        });

        watcher.onDidCreate((uri: any) => {
            callback({ type: 'create', path: uri.fsPath });
        });

        watcher.onDidDelete((uri: any) => {
            callback({ type: 'delete', path: uri.fsPath });
        });
    }

    // ===== CLEANUP =====

    dispose(): void {
        if (this.websocket) {
            this.websocket.close();
            this.websocket = undefined;
        }

        if (this.serverProcess) {
            this.serverProcess.kill();
            this.serverProcess = undefined;
        }

        this.isServerRunning = false;
        this.messageQueue = [];
    }
}
