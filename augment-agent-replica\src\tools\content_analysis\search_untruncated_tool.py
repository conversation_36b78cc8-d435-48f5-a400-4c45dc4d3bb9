"""
Search Untruncated Tool - Exact replica of Augment Agent's search-untruncated functionality
Search for a term within untruncated content
"""

from typing import Dict, Any
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult
from .content_manager import content_manager

class SearchUntruncatedTool(BaseTool):
    """
    Search for a term within untruncated content
    Exact replica of original Augment Agent search-untruncated tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.CONTENT_ANALYSIS
    
    def _get_description(self) -> str:
        return """Search for a term within untruncated content"""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "reference_id": {
                    "description": "The reference ID of the truncated content (found in the truncation footer)",
                    "type": "string"
                },
                "search_term": {
                    "description": "The term to search for within the content",
                    "type": "string"
                },
                "context_lines": {
                    "description": "Number of context lines to include before and after matches (default: 2)",
                    "type": "integer",
                    "default": 2
                }
            },
            "required": ["reference_id", "search_term"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the search-untruncated tool"""
        try:
            reference_id = kwargs.get('reference_id', '')
            search_term = kwargs.get('search_term', '')
            context_lines = kwargs.get('context_lines', 2)
            
            # Validate parameters
            if not reference_id:
                return ToolResult(
                    success=False,
                    error="reference_id parameter is required"
                )
            
            if not search_term:
                return ToolResult(
                    success=False,
                    error="search_term parameter is required"
                )
            
            if not isinstance(context_lines, int) or context_lines < 0:
                context_lines = 2
            
            # Limit context lines to reasonable range
            context_lines = min(context_lines, 10)
            
            # Get content reference
            content_ref = content_manager.get_content_reference(reference_id)
            if not content_ref:
                return ToolResult(
                    success=False,
                    error=f"Content reference '{reference_id}' not found"
                )
            
            # Search for the term
            matches = content_manager.search_content(
                reference_id=reference_id,
                search_term=search_term,
                context_lines=context_lines,
                case_sensitive=False  # Default to case-insensitive search
            )
            
            if not matches:
                return ToolResult(
                    success=True,
                    data={
                        'reference_id': reference_id,
                        'content_title': content_ref.title,
                        'content_type': content_ref.content_type,
                        'total_lines': content_ref.total_lines,
                        'search_term': search_term,
                        'context_lines': context_lines,
                        'matches_found': 0,
                        'matches': [],
                        'message': f"No matches found for '{search_term}'"
                    }
                )
            
            # Format matches for display
            formatted_matches = []
            
            for match in matches:
                # Format context with line numbers and highlighting
                formatted_context = []
                
                for context_line in match['context']:
                    line_num = context_line['line_number']
                    content = context_line['content']
                    is_match = context_line['is_match']
                    
                    # Add marker for match line
                    marker = "*" if is_match else " "
                    formatted_line = f"{line_num:6d}{marker}\t{content}"
                    
                    formatted_context.append({
                        'line_number': line_num,
                        'content': content,
                        'formatted': formatted_line,
                        'is_match': is_match
                    })
                
                formatted_matches.append({
                    'line_number': match['line_number'],
                    'line_content': match['line_content'],
                    'match_positions': match['match_positions'],
                    'context': formatted_context,
                    'context_text': '\n'.join([ctx['formatted'] for ctx in formatted_context])
                })
            
            # Create summary text
            summary_lines = []
            for match in formatted_matches:
                summary_lines.append(match['context_text'])
                summary_lines.append('')  # Empty line between matches
            
            summary_text = '\n'.join(summary_lines).rstrip()
            
            return ToolResult(
                success=True,
                data={
                    'reference_id': reference_id,
                    'content_title': content_ref.title,
                    'content_type': content_ref.content_type,
                    'total_lines': content_ref.total_lines,
                    'search_term': search_term,
                    'context_lines': context_lines,
                    'matches_found': len(matches),
                    'matches': formatted_matches,
                    'summary': summary_text,
                    'message': f"Found {len(matches)} match(es) for '{search_term}'"
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error searching content: {str(e)}"
            )
