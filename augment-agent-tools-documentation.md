# Augment Agent - Complete Tools & Workflow Documentation

## Overview
Augment Agent is an AI coding assistant with access to 25+ specialized tools for comprehensive software development workflows. This document covers all available tools, their capabilities, and typical usage patterns.

## 🔧 Core Development Tools

### 1. File Management Tools

#### `view` - File/Directory Viewer with Regex Search
**Capabilities:**
- View files with line numbers (`cat -n` equivalent)
- List directory contents (2 levels deep)
- Advanced regex search within files
- Context-aware line viewing with ranges

**Workflow:**
```bash
# View entire file
view(path="src/main.py", type="file")

# Search for specific patterns
view(path="src/main.py", type="file", search_query_regex="class.*Controller")

# View specific line ranges
view(path="src/main.py", type="file", view_range=[100, 200])
```

#### `str-replace-editor` - Precise File Editing
**Capabilities:**
- String replacement with exact line number targeting
- Multiple replacements in single call
- Content insertion at specific lines
- Preserves file formatting

**Workflow:**
```bash
# Replace specific content
str-replace-editor(
    command="str_replace",
    path="src/main.py",
    old_str="def old_function():",
    new_str="def new_function():",
    old_str_start_line_number=45,
    old_str_end_line_number=45
)
```

#### `save-file` - Create New Files
**Capabilities:**
- Create new files with content
- Automatic newline handling
- 300-line limit per creation

#### `remove-files` - Safe File Deletion
**Capabilities:**
- Safe file deletion (user can undo)
- Batch file removal

### 2. Code Intelligence Tools

#### `codebase-retrieval` - Advanced Code Search
**Capabilities:**
- Natural language code search
- Real-time codebase indexing
- Cross-language retrieval
- Semantic understanding of code relationships

**Workflow:**
```bash
# Before making any edits, always search for context
codebase-retrieval("Find all classes that implement authentication")
codebase-retrieval("Show me database connection methods and configuration")
```

#### `diagnostics` - IDE Error Detection
**Capabilities:**
- Get compilation errors, warnings, linting issues
- Multi-file diagnostic analysis

**Workflow:**
```bash
diagnostics(paths=["src/main.py", "src/utils.py"])
```

## 🚀 Process Management Tools

### 3. Process Execution

#### `launch-process` - Command Execution
**Capabilities:**
- Interactive terminal processes (`wait=true`)
- Background processes (`wait=false`)
- Custom working directory
- Timeout management

**Workflow:**
```bash
# Run tests and wait for completion
launch-process(
    command="npm test",
    wait=true,
    max_wait_seconds=300,
    cwd="/workspace"
)

# Start development server in background
launch-process(
    command="npm run dev",
    wait=false,
    max_wait_seconds=60,
    cwd="/workspace"
)
```

#### `read-process` / `write-process` / `kill-process`
**Capabilities:**
- Read process output
- Send input to processes
- Terminate processes by terminal ID

#### `list-processes` - Process Management
**Capabilities:**
- View all active terminals and their states

### 4. Terminal Integration

#### `read-terminal` - Terminal Output Reading
**Capabilities:**
- Read visible terminal content
- Selected text reading

## 🌐 Web & Research Tools

### 5. Web Capabilities

#### `web-search` - Google Search Integration
**Capabilities:**
- Custom search API integration
- Configurable result count (1-10)
- Markdown formatted results

**Workflow:**
```bash
web-search(query="React hooks best practices", num_results=5)
```

#### `web-fetch` - Web Content Extraction
**Capabilities:**
- Convert web pages to Markdown
- Clean content extraction

#### `open-browser` - Browser Integration
**Capabilities:**
- Open URLs in default browser
- Avoid duplicate opens (user experience optimization)

## 📋 Task Management System

### 6. Project Organization Tools

#### `view_tasklist` - Task Overview
**Capabilities:**
- View current task hierarchy
- Track progress across complex projects

#### `add_tasks` - Task Creation
**Capabilities:**
- Single or batch task creation
- Hierarchical task structure (parent/subtasks)
- Task positioning (after specific tasks)

**Workflow:**
```bash
# Add single task
add_tasks(
    name="Implement user authentication",
    description="Create login/logout functionality with JWT tokens"
)

# Add multiple related tasks
add_tasks(tasks=[
    {
        "name": "Setup database schema",
        "description": "Create user tables and relationships"
    },
    {
        "name": "Implement API endpoints",
        "description": "Create auth routes and middleware"
    }
])
```

#### `update_tasks` - Task State Management
**Capabilities:**
- Single or batch task updates
- State transitions: NOT_STARTED → IN_PROGRESS → COMPLETE
- Task property modifications

**Workflow:**
```bash
# Batch update for workflow efficiency
update_tasks(tasks=[
    {"task_id": "abc-123", "state": "COMPLETE"},
    {"task_id": "def-456", "state": "IN_PROGRESS"}
])
```

#### `reorganize_tasklist` - Structure Management
**Capabilities:**
- Major task hierarchy restructuring
- Task reordering and regrouping

## 🧠 Memory & Documentation

### 7. Knowledge Management

#### `remember` - Long-term Memory
**Capabilities:**
- Store important information across sessions
- User preferences and project context

**Workflow:**
```bash
remember("User prefers React with TypeScript for frontend projects")
```

#### `render-mermaid` - Diagram Creation
**Capabilities:**
- Interactive Mermaid diagrams
- Pan/zoom controls
- Copy functionality

**Workflow:**
```bash
render-mermaid(
    title="System Architecture",
    diagram_definition="""
    graph TD
        A[Frontend] --> B[API Gateway]
        B --> C[Microservices]
        C --> D[Database]
    """
)
```

## 📊 Content Analysis Tools

### 8. Advanced Content Handling

#### `view-range-untruncated` - Extended Content View
**Capabilities:**
- View specific ranges from truncated content
- Reference ID based access

#### `search-untruncated` - Deep Content Search
**Capabilities:**
- Search within truncated content
- Configurable context lines

## 🔄 Typical Workflows

### Complete Development Workflow

1. **Information Gathering Phase**
   ```bash
   # Understand the codebase
   codebase-retrieval("Show me the current project structure and main components")
   view(".", type="directory")
   
   # Check for existing implementations
   codebase-retrieval("Find existing authentication or user management code")
   ```

2. **Planning Phase**
   ```bash
   # Create structured task breakdown
   add_tasks(tasks=[...])  # Detailed task planning
   view_tasklist()         # Review structure
   ```

3. **Implementation Phase**
   ```bash
   # Before any edits
   codebase-retrieval("Detailed information about classes/methods I need to modify")
   
   # Make precise edits
   str-replace-editor(...)
   
   # Check for issues
   diagnostics(paths=["modified-files"])
   ```

4. **Testing & Validation Phase**
   ```bash
   # Run tests
   launch-process(command="npm test", wait=true, ...)
   
   # Check results
   read-process(terminal_id=1, wait=true, ...)
   ```

5. **Progress Tracking**
   ```bash
   # Update task states efficiently
   update_tasks(tasks=[
       {"task_id": "current", "state": "COMPLETE"},
       {"task_id": "next", "state": "IN_PROGRESS"}
   ])
   ```

### Package Management Best Practices

**Always use package managers instead of manual file editing:**
- JavaScript: `npm install package-name`
- Python: `pip install package-name`
- Rust: `cargo add package-name`
- Go: `go get package-name`

### Error Recovery Patterns

1. **Diagnostic-Driven Debugging**
   ```bash
   diagnostics(paths=["problematic-files"])
   # Analyze errors, then fix systematically
   ```

2. **Iterative Testing**
   ```bash
   # Write tests first, then iterate until passing
   launch-process(command="test-command", ...)
   # Fix issues based on test output
   # Repeat until success
   ```

## 🎯 Key Principles

1. **Information First**: Always gather context before making changes
2. **Batch Operations**: Use batch updates for efficiency
3. **Conservative Edits**: Respect existing codebase patterns
4. **Test-Driven**: Suggest testing after code changes
5. **User Permission**: Ask before potentially destructive actions
6. **Progress Tracking**: Use task management for complex work

## 📝 Code Display Standards

When showing code to users, always use:
```xml
<augment_code_snippet path="file/path.py" mode="EXCERPT">
````python
# Code content here (max 10 lines)
````
</augment_code_snippet>
```

This creates clickable, navigable code blocks for better user experience.
