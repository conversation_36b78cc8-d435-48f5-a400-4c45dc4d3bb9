import * as vscode from 'vscode';
import axios from 'axios';

// ===== AI PROVIDER INTERFACES =====

export interface AIProvider {
    name: string;
    displayName: string;
    apiKeyConfigKey: string;
    models: string[];
    defaultModel: string;
    maxTokens: number;
    supportsStreaming: boolean;
    supportsTools: boolean;
}

export interface ChatMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
    tool_calls?: any[];
}

export interface ChatCompletionRequest {
    messages: ChatMessage[];
    model: string;
    max_tokens?: number;
    temperature?: number;
    stream?: boolean;
    tools?: any[];
}

export interface ChatCompletionResponse {
    content: string;
    tool_calls?: any[];
    usage?: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
    model: string;
}

// ===== AI PROVIDER CONFIGURATIONS =====

export const AI_PROVIDERS: Record<string, AIProvider> = {
    mistral: {
        name: 'mistral',
        displayName: 'Mistral AI',
        apiKeyConfigKey: 'augmentAgent.mistralApiKey',
        models: [
            'mistral-large-latest',
            'mistral-medium-latest', 
            'mistral-small-latest',
            'codestral-latest',
            'mistral-7b-instruct',
            'mixtral-8x7b-instruct'
        ],
        defaultModel: 'mistral-large-latest',
        maxTokens: 32768,
        supportsStreaming: true,
        supportsTools: true
    },
    
    gemini: {
        name: 'gemini',
        displayName: 'Google Gemini',
        apiKeyConfigKey: 'augmentAgent.geminiApiKey',
        models: [
            'gemini-1.5-flash-8b',
            'gemini-1.5-flash',
            'gemini-2.0-flash',
            'gemini-2.0-flash-lite',
        ],
        defaultModel: 'gemini-2.0-flash',
        maxTokens: 30720,
        supportsStreaming: true,
        supportsTools: true
    },

    openai: {
        name: 'openai',
        displayName: 'OpenAI',
        apiKeyConfigKey: 'augmentAgent.openaiApiKey',
        models: [
            'gpt-4-turbo',
            'gpt-4',
            'gpt-3.5-turbo',
            'gpt-4-vision-preview'
        ],
        defaultModel: 'gpt-4-turbo',
        maxTokens: 4096,
        supportsStreaming: true,
        supportsTools: true
    },

    anthropic: {
        name: 'anthropic',
        displayName: 'Anthropic Claude',
        apiKeyConfigKey: 'augmentAgent.anthropicApiKey',
        models: [
            'claude-3-opus-20240229',
            'claude-3-sonnet-20240229',
            'claude-3-haiku-20240307',
            'claude-2.1'
        ],
        defaultModel: 'claude-3-sonnet-20240229',
        maxTokens: 4096,
        supportsStreaming: true,
        supportsTools: true
    }
};

// ===== MULTI-AI PROVIDER CLIENT =====

export class MultiAIProviderClient {
    private currentProvider: string;
    private providers: Record<string, AIProvider>;

    constructor() {
        this.providers = AI_PROVIDERS;
        this.currentProvider = this.getConfiguredProvider();
    }

    private getConfiguredProvider(): string {
        const config = vscode.workspace.getConfiguration('augmentAgent');
        const provider = config.get<string>('aiProvider', 'mistral');
        
        // Check if API key is configured for the provider
        const apiKey = config.get<string>(this.providers[provider]?.apiKeyConfigKey);
        if (!apiKey) {
            // Fallback to first provider with API key
            for (const [name, providerConfig] of Object.entries(this.providers)) {
                const key = config.get<string>(providerConfig.apiKeyConfigKey);
                if (key) {
                    return name;
                }
            }
            // Default to mistral if no API keys found
            return 'mistral';
        }
        
        return provider;
    }

    getCurrentProvider(): AIProvider {
        return this.providers[this.currentProvider];
    }

    setProvider(providerName: string): boolean {
        if (this.providers[providerName]) {
            this.currentProvider = providerName;
            return true;
        }
        return false;
    }

    getAvailableProviders(): AIProvider[] {
        return Object.values(this.providers);
    }

    getConfiguredProviders(): AIProvider[] {
        const config = vscode.workspace.getConfiguration('augmentAgent');
        return Object.values(this.providers).filter(provider => {
            const apiKey = config.get<string>(provider.apiKeyConfigKey);
            return apiKey && apiKey.trim() !== '';
        });
    }

    async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
        const provider = this.getCurrentProvider();
        const config = vscode.workspace.getConfiguration('augmentAgent');
        const apiKey = config.get<string>(provider.apiKeyConfigKey);

        if (!apiKey) {
            throw new Error(`API key not configured for ${provider.displayName}`);
        }

        switch (provider.name) {
            case 'mistral':
                return this.mistralChatCompletion(request, apiKey);
            case 'gemini':
                return this.geminiChatCompletion(request, apiKey);
            case 'openai':
                return this.openaiChatCompletion(request, apiKey);
            case 'anthropic':
                return this.anthropicChatCompletion(request, apiKey);
            default:
                throw new Error(`Unsupported provider: ${provider.name}`);
        }
    }

    private async mistralChatCompletion(request: ChatCompletionRequest, apiKey: string): Promise<ChatCompletionResponse> {
        try {
            const response = await axios.post('https://api.mistral.ai/v1/chat/completions', {
                model: request.model,
                messages: request.messages,
                max_tokens: request.max_tokens,
                temperature: request.temperature,
                stream: false,
                tools: request.tools
            }, {
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            return {
                content: response.data.choices[0].message.content,
                tool_calls: response.data.choices[0].message.tool_calls,
                usage: response.data.usage,
                model: response.data.model
            };
        } catch (error: any) {
            throw new Error(`Mistral API error: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    private async geminiChatCompletion(request: ChatCompletionRequest, apiKey: string): Promise<ChatCompletionResponse> {
        try {
            // Convert messages to Gemini format
            const contents = request.messages
                .filter(msg => msg.role !== 'system')
                .map(msg => ({
                    role: msg.role === 'assistant' ? 'model' : 'user',
                    parts: [{ text: msg.content }]
                }));

            const systemInstruction = request.messages.find(msg => msg.role === 'system')?.content;

            const response = await axios.post(
                `https://generativelanguage.googleapis.com/v1beta/models/${request.model}:generateContent?key=${apiKey}`,
                {
                    contents,
                    systemInstruction: systemInstruction ? { parts: [{ text: systemInstruction }] } : undefined,
                    generationConfig: {
                        maxOutputTokens: request.max_tokens,
                        temperature: request.temperature
                    }
                },
                {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );

            return {
                content: response.data.candidates[0].content.parts[0].text,
                usage: {
                    prompt_tokens: response.data.usageMetadata?.promptTokenCount || 0,
                    completion_tokens: response.data.usageMetadata?.candidatesTokenCount || 0,
                    total_tokens: response.data.usageMetadata?.totalTokenCount || 0
                },
                model: request.model
            };
        } catch (error: any) {
            throw new Error(`Gemini API error: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    private async openaiChatCompletion(request: ChatCompletionRequest, apiKey: string): Promise<ChatCompletionResponse> {
        try {
            const response = await axios.post('https://api.openai.com/v1/chat/completions', {
                model: request.model,
                messages: request.messages,
                max_tokens: request.max_tokens,
                temperature: request.temperature,
                stream: false,
                tools: request.tools
            }, {
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            return {
                content: response.data.choices[0].message.content,
                tool_calls: response.data.choices[0].message.tool_calls,
                usage: response.data.usage,
                model: response.data.model
            };
        } catch (error: any) {
            throw new Error(`OpenAI API error: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    private async anthropicChatCompletion(request: ChatCompletionRequest, apiKey: string): Promise<ChatCompletionResponse> {
        try {
            // Convert messages to Anthropic format
            const messages = request.messages.filter(msg => msg.role !== 'system');
            const systemMessage = request.messages.find(msg => msg.role === 'system')?.content;

            const response = await axios.post('https://api.anthropic.com/v1/messages', {
                model: request.model,
                max_tokens: request.max_tokens,
                temperature: request.temperature,
                system: systemMessage,
                messages: messages.map(msg => ({
                    role: msg.role,
                    content: msg.content
                }))
            }, {
                headers: {
                    'x-api-key': apiKey,
                    'Content-Type': 'application/json',
                    'anthropic-version': '2023-06-01'
                }
            });

            return {
                content: response.data.content[0].text,
                usage: {
                    prompt_tokens: response.data.usage.input_tokens,
                    completion_tokens: response.data.usage.output_tokens,
                    total_tokens: response.data.usage.input_tokens + response.data.usage.output_tokens
                },
                model: response.data.model
            };
        } catch (error: any) {
            throw new Error(`Anthropic API error: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    async *streamChatCompletion(request: ChatCompletionRequest): AsyncGenerator<string> {
        const provider = this.getCurrentProvider();
        
        if (!provider.supportsStreaming) {
            const response = await this.chatCompletion(request);
            yield response.content;
            return;
        }

        // For now, fallback to non-streaming
        // TODO: Implement streaming for each provider
        const response = await this.chatCompletion(request);
        const words = response.content.split(' ');
        
        for (const word of words) {
            yield word + ' ';
            await new Promise(resolve => setTimeout(resolve, 50));
        }
    }

    validateConfiguration(): { valid: boolean; errors: string[] } {
        const errors: string[] = [];
        const config = vscode.workspace.getConfiguration('augmentAgent');
        
        const configuredProviders = this.getConfiguredProviders();
        if (configuredProviders.length === 0) {
            errors.push('No AI providers configured. Please add at least one API key.');
        }

        const currentProvider = this.getCurrentProvider();
        const apiKey = config.get<string>(currentProvider.apiKeyConfigKey);
        if (!apiKey) {
            errors.push(`API key not configured for current provider: ${currentProvider.displayName}`);
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }
}
