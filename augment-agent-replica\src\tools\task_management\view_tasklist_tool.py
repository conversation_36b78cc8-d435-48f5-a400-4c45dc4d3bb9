"""
View Tasklist Tool - Exact replica of Augment Agent's view_tasklist functionality
View the current task list for the conversation
"""

from typing import Dict, Any
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult
from .task_manager import task_manager

class ViewTasklistTool(BaseTool):
    """
    View the current task list for the conversation
    Exact replica of original Augment Agent view_tasklist tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.TASK_MANAGEMENT
    
    def _get_description(self) -> str:
        return """View the current task list for the conversation."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {},
            "required": []
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the view_tasklist tool"""
        try:
            # Get conversation ID from context if available
            conversation_id = kwargs.get('_context', {}).get('conversation_id', 'default')
            task_manager.set_conversation_id(conversation_id)
            
            # Get task hierarchy
            hierarchy = task_manager.get_task_hierarchy()
            
            # Get markdown representation
            markdown = task_manager.get_task_markdown()
            
            # Get summary statistics
            tasks = task_manager.get_tasks()
            summary = {
                'total_tasks': len(tasks),
                'not_started': len([t for t in tasks if t.state.value == 'NOT_STARTED']),
                'in_progress': len([t for t in tasks if t.state.value == 'IN_PROGRESS']),
                'complete': len([t for t in tasks if t.state.value == 'COMPLETE']),
                'cancelled': len([t for t in tasks if t.state.value == 'CANCELLED'])
            }
            
            return ToolResult(
                success=True,
                data={
                    'conversation_id': conversation_id,
                    'summary': summary,
                    'hierarchy': hierarchy,
                    'markdown': markdown,
                    'task_count': len(tasks)
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error viewing task list: {str(e)}"
            )
