"""
Setup script for Augment Agent Replica
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="augment-agent-replica",
    version="1.0.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="Complete replica of Augment Agent with all tools and capabilities",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/augment-agent-replica",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.9",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "pytest-cov>=4.1.0",
            "black>=23.11.0",
            "flake8>=6.1.0",
            "mypy>=1.7.0",
        ],
        "web": [
            "fastapi>=0.104.1",
            "uvicorn>=0.24.0",
            "websockets>=12.0",
        ],
        "full": [
            "selenium>=4.15.0",
            "elasticsearch>=8.11.0",
            "redis>=5.0.1",
            "celery>=5.3.4",
        ]
    },
    entry_points={
        "console_scripts": [
            "augment-agent-replica=main:main",
            "aar=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.yml", "*.yaml", "*.json"],
    },
    zip_safe=False,
)
