"""
Process Manager - Central management for all processes and terminals
"""

import asyncio
import subprocess
import threading
import time
import os
import signal
import psutil
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ProcessState(Enum):
    STARTING = "starting"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    KILLED = "killed"
    TIMEOUT = "timeout"

@dataclass
class ProcessInfo:
    """Information about a managed process"""
    terminal_id: int
    command: str
    cwd: str
    state: ProcessState
    pid: Optional[int] = None
    return_code: Optional[int] = None
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    output_buffer: List[str] = field(default_factory=list)
    error_buffer: List[str] = field(default_factory=list)
    max_buffer_size: int = 10000
    process: Optional[subprocess.Popen] = None
    wait_mode: bool = False
    timeout: Optional[float] = None

class ProcessManager:
    """Central manager for all processes and terminals"""
    
    def __init__(self):
        self.processes: Dict[int, ProcessInfo] = {}
        self.next_terminal_id = 1
        self.lock = threading.Lock()
        self.active_terminal_id: Optional[int] = None
        
        # Background thread for monitoring processes
        self.monitor_thread = threading.Thread(target=self._monitor_processes, daemon=True)
        self.monitor_thread.start()
    
    def launch_process(
        self,
        command: str,
        cwd: str,
        wait: bool = False,
        max_wait_seconds: float = 600,
        shell: bool = True
    ) -> int:
        """Launch a new process and return terminal ID"""
        
        with self.lock:
            terminal_id = self.next_terminal_id
            self.next_terminal_id += 1
            
            # Create process info
            process_info = ProcessInfo(
                terminal_id=terminal_id,
                command=command,
                cwd=cwd,
                state=ProcessState.STARTING,
                wait_mode=wait,
                timeout=max_wait_seconds if wait else None
            )
            
            self.processes[terminal_id] = process_info
            self.active_terminal_id = terminal_id
        
        # Start process in background thread
        thread = threading.Thread(
            target=self._start_process,
            args=(terminal_id, command, cwd, shell),
            daemon=True
        )
        thread.start()
        
        # If wait mode, wait for completion
        if wait:
            self._wait_for_process(terminal_id, max_wait_seconds)
        
        return terminal_id
    
    def _start_process(self, terminal_id: int, command: str, cwd: str, shell: bool):
        """Start the actual process"""
        try:
            process_info = self.processes[terminal_id]
            
            # Determine shell based on OS
            if os.name == 'nt':  # Windows
                shell_cmd = ['cmd', '/c'] if shell else None
            else:  # Unix-like
                shell_cmd = ['/bin/bash', '-c'] if shell else None
            
            if shell_cmd and shell:
                full_command = shell_cmd + [command]
            else:
                full_command = command.split() if isinstance(command, str) else command
            
            # Start process
            process = subprocess.Popen(
                full_command,
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            with self.lock:
                process_info.process = process
                process_info.pid = process.pid
                process_info.state = ProcessState.RUNNING
            
            # Start output readers
            self._start_output_readers(terminal_id, process)
            
        except Exception as e:
            logger.error(f"Error starting process {terminal_id}: {e}")
            with self.lock:
                process_info = self.processes.get(terminal_id)
                if process_info:
                    process_info.state = ProcessState.FAILED
                    process_info.error_buffer.append(f"Failed to start: {str(e)}")
    
    def _start_output_readers(self, terminal_id: int, process: subprocess.Popen):
        """Start threads to read stdout and stderr"""
        
        def read_stdout():
            try:
                for line in iter(process.stdout.readline, ''):
                    if line:
                        with self.lock:
                            process_info = self.processes.get(terminal_id)
                            if process_info:
                                process_info.output_buffer.append(line.rstrip())
                                # Limit buffer size
                                if len(process_info.output_buffer) > process_info.max_buffer_size:
                                    process_info.output_buffer = process_info.output_buffer[-process_info.max_buffer_size:]
            except Exception as e:
                logger.error(f"Error reading stdout for {terminal_id}: {e}")
        
        def read_stderr():
            try:
                for line in iter(process.stderr.readline, ''):
                    if line:
                        with self.lock:
                            process_info = self.processes.get(terminal_id)
                            if process_info:
                                process_info.error_buffer.append(line.rstrip())
                                # Limit buffer size
                                if len(process_info.error_buffer) > process_info.max_buffer_size:
                                    process_info.error_buffer = process_info.error_buffer[-process_info.max_buffer_size:]
            except Exception as e:
                logger.error(f"Error reading stderr for {terminal_id}: {e}")
        
        stdout_thread = threading.Thread(target=read_stdout, daemon=True)
        stderr_thread = threading.Thread(target=read_stderr, daemon=True)
        
        stdout_thread.start()
        stderr_thread.start()
    
    def _wait_for_process(self, terminal_id: int, timeout: float):
        """Wait for process to complete"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            with self.lock:
                process_info = self.processes.get(terminal_id)
                if not process_info or process_info.state != ProcessState.RUNNING:
                    break
            
            time.sleep(0.1)
        
        # Check if timeout occurred
        with self.lock:
            process_info = self.processes.get(terminal_id)
            if process_info and process_info.state == ProcessState.RUNNING:
                process_info.state = ProcessState.TIMEOUT
                if process_info.process:
                    try:
                        process_info.process.terminate()
                    except:
                        pass
    
    def _monitor_processes(self):
        """Background thread to monitor process states"""
        while True:
            try:
                with self.lock:
                    for terminal_id, process_info in list(self.processes.items()):
                        if process_info.process and process_info.state == ProcessState.RUNNING:
                            # Check if process is still running
                            return_code = process_info.process.poll()
                            if return_code is not None:
                                process_info.return_code = return_code
                                process_info.end_time = time.time()
                                process_info.state = ProcessState.COMPLETED if return_code == 0 else ProcessState.FAILED
                
                time.sleep(1)
            except Exception as e:
                logger.error(f"Error in process monitor: {e}")
                time.sleep(5)
    
    def read_process(self, terminal_id: int, wait: bool = False, max_wait_seconds: float = 60) -> Dict[str, Any]:
        """Read output from a process"""
        if terminal_id not in self.processes:
            return {
                'success': False,
                'error': f'Terminal {terminal_id} not found'
            }
        
        if wait:
            start_time = time.time()
            while time.time() - start_time < max_wait_seconds:
                with self.lock:
                    process_info = self.processes[terminal_id]
                    if process_info.state != ProcessState.RUNNING:
                        break
                time.sleep(0.1)
        
        with self.lock:
            process_info = self.processes[terminal_id]
            
            return {
                'success': True,
                'terminal_id': terminal_id,
                'state': process_info.state.value,
                'return_code': process_info.return_code,
                'stdout': '\n'.join(process_info.output_buffer),
                'stderr': '\n'.join(process_info.error_buffer),
                'pid': process_info.pid,
                'command': process_info.command,
                'cwd': process_info.cwd,
                'start_time': process_info.start_time,
                'end_time': process_info.end_time
            }
    
    def write_process(self, terminal_id: int, input_text: str) -> Dict[str, Any]:
        """Write input to a process"""
        if terminal_id not in self.processes:
            return {
                'success': False,
                'error': f'Terminal {terminal_id} not found'
            }
        
        with self.lock:
            process_info = self.processes[terminal_id]
            
            if not process_info.process or process_info.state != ProcessState.RUNNING:
                return {
                    'success': False,
                    'error': f'Process {terminal_id} is not running'
                }
            
            try:
                process_info.process.stdin.write(input_text)
                if not input_text.endswith('\n'):
                    process_info.process.stdin.write('\n')
                process_info.process.stdin.flush()
                
                return {
                    'success': True,
                    'terminal_id': terminal_id,
                    'input_sent': input_text
                }
            except Exception as e:
                return {
                    'success': False,
                    'error': f'Failed to write to process: {str(e)}'
                }
    
    def kill_process(self, terminal_id: int) -> Dict[str, Any]:
        """Kill a process"""
        if terminal_id not in self.processes:
            return {
                'success': False,
                'error': f'Terminal {terminal_id} not found'
            }
        
        with self.lock:
            process_info = self.processes[terminal_id]
            
            if not process_info.process:
                return {
                    'success': False,
                    'error': f'No process associated with terminal {terminal_id}'
                }
            
            try:
                # Try graceful termination first
                process_info.process.terminate()
                
                # Wait a bit for graceful shutdown
                try:
                    process_info.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # Force kill if graceful termination failed
                    process_info.process.kill()
                    process_info.process.wait()
                
                process_info.state = ProcessState.KILLED
                process_info.end_time = time.time()
                
                return {
                    'success': True,
                    'terminal_id': terminal_id,
                    'message': 'Process terminated'
                }
            except Exception as e:
                return {
                    'success': False,
                    'error': f'Failed to kill process: {str(e)}'
                }
    
    def list_processes(self) -> List[Dict[str, Any]]:
        """List all known processes"""
        with self.lock:
            processes = []
            for terminal_id, process_info in self.processes.items():
                processes.append({
                    'terminal_id': terminal_id,
                    'command': process_info.command,
                    'cwd': process_info.cwd,
                    'state': process_info.state.value,
                    'pid': process_info.pid,
                    'return_code': process_info.return_code,
                    'start_time': process_info.start_time,
                    'end_time': process_info.end_time,
                    'wait_mode': process_info.wait_mode
                })
            return processes
    
    def read_terminal(self, only_selected: bool = False) -> str:
        """Read output from the active terminal"""
        if only_selected:
            # For now, just return empty since we don't have selection support
            return ""
        
        if self.active_terminal_id is None:
            return "No active terminal"
        
        result = self.read_process(self.active_terminal_id)
        if result['success']:
            output = result['stdout']
            if result['stderr']:
                output += '\n--- STDERR ---\n' + result['stderr']
            return output
        else:
            return f"Error reading terminal: {result['error']}"
    
    def cleanup_old_processes(self, max_age_hours: int = 24):
        """Clean up old completed processes"""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        with self.lock:
            to_remove = []
            for terminal_id, process_info in self.processes.items():
                if (process_info.state in [ProcessState.COMPLETED, ProcessState.FAILED, ProcessState.KILLED] and
                    process_info.end_time and
                    current_time - process_info.end_time > max_age_seconds):
                    to_remove.append(terminal_id)
            
            for terminal_id in to_remove:
                del self.processes[terminal_id]

# Global process manager instance
process_manager = ProcessManager()
