Requirement already satisfied: fastapi in c:\users\<USER>\appdata\roaming\python\python310\site-packages (0.115.9)
Requirement already satisfied: starlette<0.46.0,>=0.40.0 in c:\python310\lib\site-packages (from fastapi) (0.45.3)
Requirement already satisfied: pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from fastapi) (2.11.5)
Requirement already satisfied: typing-extensions>=4.8.0 in c:\python310\lib\site-packages (from fastapi) (4.12.2)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi) (0.4.1)
Requirement already satisfied: anyio<5,>=3.6.2 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from starlette<0.46.0,>=0.40.0->fastapi) (4.9.0)
Requirement already satisfied: exceptiongroup>=1.0.2 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from anyio<5,>=3.6.2->starlette<0.46.0,>=0.40.0->fastapi) (1.3.0)
Requirement already satisfied: idna>=2.8 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from anyio<5,>=3.6.2->starlette<0.46.0,>=0.40.0->fastapi) (3.10)
Requirement already satisfied: sniffio>=1.1 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from anyio<5,>=3.6.2->starlette<0.46.0,>=0.40.0->fastapi) (1.3.1)
