"""
Task Manager - Central management for task lists and hierarchies
"""

import uuid
import json
import sqlite3
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field, asdict
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class TaskState(Enum):
    NOT_STARTED = "NOT_STARTED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETE = "COMPLETE"
    CANCELLED = "CANCELLED"

@dataclass
class Task:
    """Represents a task in the task management system"""
    task_id: str
    name: str
    description: str
    state: TaskState = TaskState.NOT_STARTED
    parent_task_id: Optional[str] = None
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    completed_at: Optional[float] = None
    order_index: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary"""
        data = asdict(self)
        data['state'] = self.state.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Task':
        """Create task from dictionary"""
        data = data.copy()
        data['state'] = TaskState(data['state'])
        return cls(**data)

class TaskManager:
    """Central manager for task lists and hierarchies"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or ":memory:"
        self.conversation_tasks: Dict[str, List[Task]] = {}
        self.current_conversation_id = "default"
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database for persistent storage"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS tasks (
                    task_id TEXT PRIMARY KEY,
                    conversation_id TEXT NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    state TEXT NOT NULL,
                    parent_task_id TEXT,
                    created_at REAL,
                    updated_at REAL,
                    completed_at REAL,
                    order_index INTEGER DEFAULT 0,
                    FOREIGN KEY (parent_task_id) REFERENCES tasks (task_id)
                )
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_tasks_conversation 
                ON tasks(conversation_id)
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_tasks_parent 
                ON tasks(parent_task_id)
            ''')
    
    def set_conversation_id(self, conversation_id: str):
        """Set the current conversation ID"""
        self.current_conversation_id = conversation_id
        self._load_tasks_for_conversation(conversation_id)
    
    def _load_tasks_for_conversation(self, conversation_id: str):
        """Load tasks for a specific conversation from database"""
        if conversation_id in self.conversation_tasks:
            return
        
        tasks = []
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    'SELECT * FROM tasks WHERE conversation_id = ? ORDER BY order_index, created_at',
                    (conversation_id,)
                )
                
                for row in cursor.fetchall():
                    task_data = dict(row)
                    task_data.pop('conversation_id', None)
                    task = Task.from_dict(task_data)
                    tasks.append(task)
        
        except Exception as e:
            logger.error(f"Error loading tasks for conversation {conversation_id}: {e}")
        
        self.conversation_tasks[conversation_id] = tasks
    
    def _save_task(self, task: Task):
        """Save a task to the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO tasks 
                    (task_id, conversation_id, name, description, state, parent_task_id, 
                     created_at, updated_at, completed_at, order_index)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    task.task_id, self.current_conversation_id, task.name, task.description,
                    task.state.value, task.parent_task_id, task.created_at, task.updated_at,
                    task.completed_at, task.order_index
                ))
        except Exception as e:
            logger.error(f"Error saving task {task.task_id}: {e}")
    
    def _delete_task_from_db(self, task_id: str):
        """Delete a task from the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('DELETE FROM tasks WHERE task_id = ?', (task_id,))
        except Exception as e:
            logger.error(f"Error deleting task {task_id}: {e}")
    
    def get_tasks(self) -> List[Task]:
        """Get all tasks for the current conversation"""
        if self.current_conversation_id not in self.conversation_tasks:
            self._load_tasks_for_conversation(self.current_conversation_id)
        
        return self.conversation_tasks.get(self.current_conversation_id, [])
    
    def add_task(
        self,
        name: str,
        description: str,
        parent_task_id: Optional[str] = None,
        after_task_id: Optional[str] = None,
        state: TaskState = TaskState.NOT_STARTED
    ) -> Task:
        """Add a new task"""
        task_id = str(uuid.uuid4())
        
        # Determine order index
        order_index = 0
        tasks = self.get_tasks()
        
        if after_task_id:
            # Find the task to insert after
            for i, task in enumerate(tasks):
                if task.task_id == after_task_id:
                    order_index = task.order_index + 1
                    # Increment order_index of subsequent tasks
                    for j in range(i + 1, len(tasks)):
                        tasks[j].order_index += 1
                        self._save_task(tasks[j])
                    break
        else:
            # Add at the end
            if tasks:
                order_index = max(task.order_index for task in tasks) + 1
        
        # Create new task
        task = Task(
            task_id=task_id,
            name=name,
            description=description,
            state=state,
            parent_task_id=parent_task_id,
            order_index=order_index
        )
        
        # Add to conversation tasks
        if self.current_conversation_id not in self.conversation_tasks:
            self.conversation_tasks[self.current_conversation_id] = []
        
        self.conversation_tasks[self.current_conversation_id].append(task)
        
        # Save to database
        self._save_task(task)
        
        return task
    
    def update_task(
        self,
        task_id: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        state: Optional[TaskState] = None
    ) -> Optional[Task]:
        """Update an existing task"""
        tasks = self.get_tasks()
        
        for task in tasks:
            if task.task_id == task_id:
                if name is not None:
                    task.name = name
                if description is not None:
                    task.description = description
                if state is not None:
                    task.state = state
                    if state == TaskState.COMPLETE:
                        task.completed_at = time.time()
                    elif task.completed_at and state != TaskState.COMPLETE:
                        task.completed_at = None
                
                task.updated_at = time.time()
                self._save_task(task)
                return task
        
        return None
    
    def delete_task(self, task_id: str) -> bool:
        """Delete a task and all its subtasks"""
        tasks = self.get_tasks()
        
        # Find all tasks to delete (task and its descendants)
        tasks_to_delete = []
        
        def find_descendants(parent_id: str):
            for task in tasks:
                if task.parent_task_id == parent_id:
                    tasks_to_delete.append(task.task_id)
                    find_descendants(task.task_id)
        
        # Check if task exists
        task_exists = any(task.task_id == task_id for task in tasks)
        if not task_exists:
            return False
        
        # Add the main task and find all descendants
        tasks_to_delete.append(task_id)
        find_descendants(task_id)
        
        # Remove from conversation tasks
        self.conversation_tasks[self.current_conversation_id] = [
            task for task in tasks if task.task_id not in tasks_to_delete
        ]
        
        # Remove from database
        for tid in tasks_to_delete:
            self._delete_task_from_db(tid)
        
        return True
    
    def reorganize_tasks(self, task_hierarchy: List[Dict[str, Any]]) -> bool:
        """Reorganize tasks based on new hierarchy"""
        try:
            # Clear current tasks
            old_tasks = self.get_tasks()
            self.conversation_tasks[self.current_conversation_id] = []
            
            # Delete all tasks from database for this conversation
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('DELETE FROM tasks WHERE conversation_id = ?', (self.current_conversation_id,))
            
            # Recreate tasks from hierarchy
            self._create_tasks_from_hierarchy(task_hierarchy, None, 0)
            
            return True
        
        except Exception as e:
            logger.error(f"Error reorganizing tasks: {e}")
            # Restore old tasks on error
            self.conversation_tasks[self.current_conversation_id] = old_tasks
            for task in old_tasks:
                self._save_task(task)
            return False
    
    def _create_tasks_from_hierarchy(
        self,
        task_hierarchy: List[Dict[str, Any]],
        parent_task_id: Optional[str],
        start_order: int
    ) -> int:
        """Recursively create tasks from hierarchy"""
        order_index = start_order
        
        for task_data in task_hierarchy:
            # Handle NEW_UUID placeholder
            task_id = task_data.get('task_id', str(uuid.uuid4()))
            if task_id == 'NEW_UUID':
                task_id = str(uuid.uuid4())
            
            # Create task
            task = Task(
                task_id=task_id,
                name=task_data['name'],
                description=task_data['description'],
                state=TaskState(task_data.get('state', 'NOT_STARTED')),
                parent_task_id=parent_task_id,
                order_index=order_index
            )
            
            # Add to conversation tasks
            self.conversation_tasks[self.current_conversation_id].append(task)
            self._save_task(task)
            
            order_index += 1
            
            # Handle subtasks
            if 'subtasks' in task_data:
                order_index = self._create_tasks_from_hierarchy(
                    task_data['subtasks'],
                    task_id,
                    order_index
                )
        
        return order_index
    
    def get_task_hierarchy(self) -> List[Dict[str, Any]]:
        """Get tasks organized in hierarchical structure"""
        tasks = self.get_tasks()
        
        # Create a map of tasks by ID
        task_map = {task.task_id: task for task in tasks}
        
        # Build hierarchy
        def build_hierarchy(parent_id: Optional[str]) -> List[Dict[str, Any]]:
            children = []
            for task in tasks:
                if task.parent_task_id == parent_id:
                    task_dict = task.to_dict()
                    subtasks = build_hierarchy(task.task_id)
                    if subtasks:
                        task_dict['subtasks'] = subtasks
                    children.append(task_dict)
            
            # Sort by order_index
            children.sort(key=lambda x: x['order_index'])
            return children
        
        return build_hierarchy(None)
    
    def get_task_markdown(self) -> str:
        """Get tasks formatted as markdown"""
        hierarchy = self.get_task_hierarchy()
        
        def format_task(task_data: Dict[str, Any], level: int = 0) -> str:
            indent = '-' * level if level > 0 else ''
            
            # State symbols
            state_symbols = {
                'NOT_STARTED': '[ ]',
                'IN_PROGRESS': '[/]',
                'COMPLETE': '[x]',
                'CANCELLED': '[-]'
            }
            
            state_symbol = state_symbols.get(task_data['state'], '[ ]')
            
            # Format task line
            line = f"{indent}{state_symbol} UUID:{task_data['task_id']} NAME:{task_data['name']} DESCRIPTION:{task_data['description']}"
            
            lines = [line]
            
            # Add subtasks
            for subtask in task_data.get('subtasks', []):
                lines.append(format_task(subtask, level + 1))
            
            return '\n'.join(lines)
        
        if not hierarchy:
            return "No tasks found."
        
        # Format all root tasks
        formatted_tasks = []
        for task in hierarchy:
            formatted_tasks.append(format_task(task))
        
        return '\n'.join(formatted_tasks)

# Global task manager instance
task_manager = TaskManager()
