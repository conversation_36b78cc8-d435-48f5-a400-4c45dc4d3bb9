"""
Diagnostics Tool - Exact replica of Augment Agent's diagnostics functionality
IDE error detection, warnings, and code analysis
"""

import os
import ast
import re
import subprocess
import json
import tempfile
from pathlib import Path
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass
import logging

from ...core.tool_executor import BaseTool, ToolCategory, ToolResult

logger = logging.getLogger(__name__)

@dataclass
class Diagnostic:
    """Represents a diagnostic issue (error, warning, info)"""
    file_path: str
    line_number: int
    column: int
    severity: str  # 'error', 'warning', 'info', 'hint'
    message: str
    code: Optional[str] = None
    source: str = 'unknown'  # 'python', 'eslint', 'tsc', etc.
    rule: Optional[str] = None

class DiagnosticsTool(BaseTool):
    """
    Get issues (errors, warnings, etc.) from the IDE
    Exact replica of original Augment Agent diagnostics tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.CODE_INTELLIGENCE
    
    def _get_description(self) -> str:
        return """Get issues (errors, warnings, etc.) from the IDE. You must provide the paths of the files for which you want to get issues."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "paths": {
                    "description": "Required list of file paths to get issues for from the IDE.",
                    "type": "array",
                    "items": {"type": "string"}
                }
            },
            "required": ["paths"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute diagnostics analysis"""
        try:
            paths = kwargs.get('paths', [])
            
            if not paths:
                return ToolResult(
                    success=False,
                    error="paths parameter is required"
                )
            
            # Get workspace root from context
            workspace_root = kwargs.get('_context', {}).get('workspace_root', os.getcwd())
            
            # Analyze each file
            all_diagnostics = []
            analysis_summary = {
                'files_analyzed': 0,
                'total_issues': 0,
                'errors': 0,
                'warnings': 0,
                'info': 0,
                'hints': 0
            }
            
            for path in paths:
                full_path = os.path.join(workspace_root, path) if not os.path.isabs(path) else path
                
                if not os.path.exists(full_path):
                    all_diagnostics.append(Diagnostic(
                        file_path=path,
                        line_number=0,
                        column=0,
                        severity='error',
                        message=f"File not found: {path}",
                        source='filesystem'
                    ))
                    continue
                
                if not os.path.isfile(full_path):
                    all_diagnostics.append(Diagnostic(
                        file_path=path,
                        line_number=0,
                        column=0,
                        severity='error',
                        message=f"Path is not a file: {path}",
                        source='filesystem'
                    ))
                    continue
                
                # Analyze file based on extension
                file_diagnostics = await self._analyze_file(full_path, path)
                all_diagnostics.extend(file_diagnostics)
                analysis_summary['files_analyzed'] += 1
            
            # Count diagnostics by severity
            for diagnostic in all_diagnostics:
                analysis_summary['total_issues'] += 1
                analysis_summary[diagnostic.severity + 's'] += 1
            
            # Format results
            formatted_diagnostics = self._format_diagnostics(all_diagnostics)
            
            return ToolResult(
                success=True,
                data={
                    'summary': analysis_summary,
                    'diagnostics': formatted_diagnostics,
                    'files_analyzed': paths
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error in diagnostics analysis: {str(e)}"
            )
    
    async def _analyze_file(self, full_path: str, relative_path: str) -> List[Diagnostic]:
        """Analyze a single file for issues"""
        diagnostics = []
        
        try:
            file_ext = Path(full_path).suffix.lower()
            
            if file_ext == '.py':
                diagnostics.extend(await self._analyze_python_file(full_path, relative_path))
            elif file_ext in ['.js', '.jsx']:
                diagnostics.extend(await self._analyze_javascript_file(full_path, relative_path))
            elif file_ext in ['.ts', '.tsx']:
                diagnostics.extend(await self._analyze_typescript_file(full_path, relative_path))
            elif file_ext in ['.json']:
                diagnostics.extend(await self._analyze_json_file(full_path, relative_path))
            elif file_ext in ['.yaml', '.yml']:
                diagnostics.extend(await self._analyze_yaml_file(full_path, relative_path))
            else:
                # Generic text analysis
                diagnostics.extend(await self._analyze_generic_file(full_path, relative_path))
        
        except Exception as e:
            diagnostics.append(Diagnostic(
                file_path=relative_path,
                line_number=0,
                column=0,
                severity='error',
                message=f"Analysis failed: {str(e)}",
                source='analyzer'
            ))
        
        return diagnostics
    
    async def _analyze_python_file(self, full_path: str, relative_path: str) -> List[Diagnostic]:
        """Analyze Python file for syntax errors and issues"""
        diagnostics = []
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 1. Syntax check using AST
            try:
                ast.parse(content)
            except SyntaxError as e:
                diagnostics.append(Diagnostic(
                    file_path=relative_path,
                    line_number=e.lineno or 0,
                    column=e.offset or 0,
                    severity='error',
                    message=f"Syntax error: {e.msg}",
                    source='python',
                    code='syntax-error'
                ))
            
            # 2. Check for common issues
            lines = content.split('\n')
            for line_num, line in enumerate(lines, 1):
                # Check for common Python issues
                
                # Unused imports (simple check)
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    import_name = self._extract_import_name(line)
                    if import_name and import_name not in content[content.find(line) + len(line):]:
                        diagnostics.append(Diagnostic(
                            file_path=relative_path,
                            line_number=line_num,
                            column=0,
                            severity='warning',
                            message=f"Potentially unused import: {import_name}",
                            source='python',
                            rule='unused-import'
                        ))
                
                # Long lines
                if len(line) > 120:
                    diagnostics.append(Diagnostic(
                        file_path=relative_path,
                        line_number=line_num,
                        column=120,
                        severity='info',
                        message=f"Line too long ({len(line)} > 120 characters)",
                        source='python',
                        rule='line-too-long'
                    ))
                
                # Trailing whitespace
                if line.endswith(' ') or line.endswith('\t'):
                    diagnostics.append(Diagnostic(
                        file_path=relative_path,
                        line_number=line_num,
                        column=len(line.rstrip()),
                        severity='info',
                        message="Trailing whitespace",
                        source='python',
                        rule='trailing-whitespace'
                    ))
            
            # 3. Try to run flake8 if available
            try:
                result = subprocess.run(
                    ['flake8', '--format=json', full_path],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0 and result.stdout:
                    flake8_issues = json.loads(result.stdout)
                    for issue in flake8_issues:
                        diagnostics.append(Diagnostic(
                            file_path=relative_path,
                            line_number=issue.get('line_number', 0),
                            column=issue.get('column_number', 0),
                            severity='warning' if issue.get('code', '').startswith('W') else 'error',
                            message=issue.get('text', ''),
                            source='flake8',
                            code=issue.get('code')
                        ))
            except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError, json.JSONDecodeError):
                pass  # flake8 not available or failed
        
        except Exception as e:
            logger.error(f"Error analyzing Python file {full_path}: {e}")
        
        return diagnostics
    
    async def _analyze_javascript_file(self, full_path: str, relative_path: str) -> List[Diagnostic]:
        """Analyze JavaScript file for issues"""
        diagnostics = []
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # Basic syntax and style checks
            for line_num, line in enumerate(lines, 1):
                # Missing semicolons (simple check)
                stripped = line.strip()
                if (stripped and 
                    not stripped.endswith((';', '{', '}', ')', ',')) and
                    not stripped.startswith(('if', 'for', 'while', 'function', 'class', '//', '/*', '*')) and
                    '=' in stripped):
                    diagnostics.append(Diagnostic(
                        file_path=relative_path,
                        line_number=line_num,
                        column=len(line),
                        severity='warning',
                        message="Missing semicolon",
                        source='javascript',
                        rule='missing-semicolon'
                    ))
                
                # Console.log statements
                if 'console.log' in line:
                    diagnostics.append(Diagnostic(
                        file_path=relative_path,
                        line_number=line_num,
                        column=line.find('console.log'),
                        severity='info',
                        message="Console.log statement found",
                        source='javascript',
                        rule='no-console'
                    ))
                
                # Long lines
                if len(line) > 120:
                    diagnostics.append(Diagnostic(
                        file_path=relative_path,
                        line_number=line_num,
                        column=120,
                        severity='info',
                        message=f"Line too long ({len(line)} > 120 characters)",
                        source='javascript',
                        rule='line-too-long'
                    ))
            
            # Try to run ESLint if available
            try:
                result = subprocess.run(
                    ['eslint', '--format=json', full_path],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.stdout:
                    eslint_results = json.loads(result.stdout)
                    for file_result in eslint_results:
                        for message in file_result.get('messages', []):
                            severity_map = {1: 'warning', 2: 'error'}
                            diagnostics.append(Diagnostic(
                                file_path=relative_path,
                                line_number=message.get('line', 0),
                                column=message.get('column', 0),
                                severity=severity_map.get(message.get('severity', 1), 'warning'),
                                message=message.get('message', ''),
                                source='eslint',
                                rule=message.get('ruleId')
                            ))
            except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError, json.JSONDecodeError):
                pass  # ESLint not available or failed
        
        except Exception as e:
            logger.error(f"Error analyzing JavaScript file {full_path}: {e}")
        
        return diagnostics
    
    async def _analyze_typescript_file(self, full_path: str, relative_path: str) -> List[Diagnostic]:
        """Analyze TypeScript file for issues"""
        diagnostics = []
        
        # First run JavaScript analysis
        diagnostics.extend(await self._analyze_javascript_file(full_path, relative_path))
        
        try:
            # Try to run TypeScript compiler
            result = subprocess.run(
                ['tsc', '--noEmit', '--pretty', 'false', full_path],
                capture_output=True,
                text=True,
                timeout=15
            )
            
            if result.stderr:
                # Parse TypeScript compiler output
                for line in result.stderr.split('\n'):
                    if full_path in line and '(' in line:
                        # Parse format: file.ts(line,col): error TS#### message
                        match = re.match(r'.*\((\d+),(\d+)\):\s*(error|warning)\s*TS(\d+):\s*(.*)', line)
                        if match:
                            line_num, col_num, severity, ts_code, message = match.groups()
                            diagnostics.append(Diagnostic(
                                file_path=relative_path,
                                line_number=int(line_num),
                                column=int(col_num),
                                severity=severity,
                                message=message.strip(),
                                source='typescript',
                                code=f'TS{ts_code}'
                            ))
        
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            pass  # TypeScript compiler not available or failed
        
        return diagnostics
    
    async def _analyze_json_file(self, full_path: str, relative_path: str) -> List[Diagnostic]:
        """Analyze JSON file for syntax errors"""
        diagnostics = []
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            try:
                json.loads(content)
            except json.JSONDecodeError as e:
                diagnostics.append(Diagnostic(
                    file_path=relative_path,
                    line_number=e.lineno,
                    column=e.colno,
                    severity='error',
                    message=f"JSON syntax error: {e.msg}",
                    source='json',
                    code='syntax-error'
                ))
        
        except Exception as e:
            logger.error(f"Error analyzing JSON file {full_path}: {e}")
        
        return diagnostics
    
    async def _analyze_yaml_file(self, full_path: str, relative_path: str) -> List[Diagnostic]:
        """Analyze YAML file for syntax errors"""
        diagnostics = []
        
        try:
            import yaml
            
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            try:
                yaml.safe_load(content)
            except yaml.YAMLError as e:
                line_num = getattr(e, 'problem_mark', None)
                diagnostics.append(Diagnostic(
                    file_path=relative_path,
                    line_number=line_num.line + 1 if line_num else 0,
                    column=line_num.column + 1 if line_num else 0,
                    severity='error',
                    message=f"YAML syntax error: {str(e)}",
                    source='yaml',
                    code='syntax-error'
                ))
        
        except ImportError:
            # PyYAML not available
            pass
        except Exception as e:
            logger.error(f"Error analyzing YAML file {full_path}: {e}")
        
        return diagnostics
    
    async def _analyze_generic_file(self, full_path: str, relative_path: str) -> List[Diagnostic]:
        """Analyze generic text file for basic issues"""
        diagnostics = []
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                # Check for very long lines
                if len(line) > 200:
                    diagnostics.append(Diagnostic(
                        file_path=relative_path,
                        line_number=line_num,
                        column=200,
                        severity='info',
                        message=f"Very long line ({len(line)} characters)",
                        source='generic',
                        rule='line-too-long'
                    ))
                
                # Check for trailing whitespace
                if line.rstrip() != line.rstrip('\n'):
                    diagnostics.append(Diagnostic(
                        file_path=relative_path,
                        line_number=line_num,
                        column=len(line.rstrip('\n')),
                        severity='info',
                        message="Trailing whitespace",
                        source='generic',
                        rule='trailing-whitespace'
                    ))
        
        except UnicodeDecodeError:
            diagnostics.append(Diagnostic(
                file_path=relative_path,
                line_number=0,
                column=0,
                severity='warning',
                message="File contains non-UTF-8 characters",
                source='generic',
                code='encoding-error'
            ))
        except Exception as e:
            logger.error(f"Error analyzing generic file {full_path}: {e}")
        
        return diagnostics
    
    def _extract_import_name(self, line: str) -> Optional[str]:
        """Extract import name from import statement"""
        line = line.strip()
        if line.startswith('import '):
            # import module
            parts = line.split()
            if len(parts) >= 2:
                return parts[1].split('.')[0]
        elif line.startswith('from '):
            # from module import name
            match = re.match(r'from\s+(\w+)', line)
            if match:
                return match.group(1)
        return None
    
    def _format_diagnostics(self, diagnostics: List[Diagnostic]) -> List[Dict[str, Any]]:
        """Format diagnostics for output"""
        formatted = []
        
        for diagnostic in diagnostics:
            formatted_diagnostic = {
                'file_path': diagnostic.file_path,
                'line_number': diagnostic.line_number,
                'column': diagnostic.column,
                'severity': diagnostic.severity,
                'message': diagnostic.message,
                'source': diagnostic.source
            }
            
            if diagnostic.code:
                formatted_diagnostic['code'] = diagnostic.code
            
            if diagnostic.rule:
                formatted_diagnostic['rule'] = diagnostic.rule
            
            formatted.append(formatted_diagnostic)
        
        return formatted
