"""
View Range Untruncated Tool - Exact replica of Augment Agent's view-range-untruncated functionality
View a specific range of lines from untruncated content
"""

from typing import Dict, Any
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult
from .content_manager import content_manager

class ViewRangeUntruncatedTool(BaseTool):
    """
    View a specific range of lines from untruncated content
    Exact replica of original Augment Agent view-range-untruncated tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.CONTENT_ANALYSIS
    
    def _get_description(self) -> str:
        return """View a specific range of lines from untruncated content"""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "reference_id": {
                    "description": "The reference ID of the truncated content (found in the truncation footer)",
                    "type": "string"
                },
                "start_line": {
                    "description": "The starting line number (1-based, inclusive)",
                    "type": "integer"
                },
                "end_line": {
                    "description": "The ending line number (1-based, inclusive)",
                    "type": "integer"
                }
            },
            "required": ["reference_id", "start_line", "end_line"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the view-range-untruncated tool"""
        try:
            reference_id = kwargs.get('reference_id', '')
            start_line = kwargs.get('start_line')
            end_line = kwargs.get('end_line')
            
            # Validate parameters
            if not reference_id:
                return ToolResult(
                    success=False,
                    error="reference_id parameter is required"
                )
            
            if start_line is None or end_line is None:
                return ToolResult(
                    success=False,
                    error="start_line and end_line parameters are required"
                )
            
            if not isinstance(start_line, int) or not isinstance(end_line, int):
                return ToolResult(
                    success=False,
                    error="start_line and end_line must be integers"
                )
            
            if start_line < 1:
                return ToolResult(
                    success=False,
                    error="start_line must be >= 1"
                )
            
            if end_line < start_line:
                return ToolResult(
                    success=False,
                    error="end_line must be >= start_line"
                )
            
            # Get content reference
            content_ref = content_manager.get_content_reference(reference_id)
            if not content_ref:
                return ToolResult(
                    success=False,
                    error=f"Content reference '{reference_id}' not found"
                )
            
            # Validate line range
            if start_line > content_ref.total_lines:
                return ToolResult(
                    success=False,
                    error=f"start_line ({start_line}) exceeds total lines ({content_ref.total_lines})"
                )
            
            # Adjust end_line if it exceeds total lines
            actual_end_line = min(end_line, content_ref.total_lines)
            
            # Get the content range
            content_range = content_manager.get_content_range(
                reference_id,
                start_line,
                actual_end_line
            )
            
            if content_range is None:
                return ToolResult(
                    success=False,
                    error=f"Failed to retrieve content range for reference '{reference_id}'"
                )
            
            # Format the content with line numbers
            lines = content_range.split('\n')
            formatted_lines = []
            
            for i, line in enumerate(lines):
                line_number = start_line + i
                formatted_lines.append(f"{line_number:6d}\t{line}")
            
            formatted_content = '\n'.join(formatted_lines)
            
            return ToolResult(
                success=True,
                data={
                    'reference_id': reference_id,
                    'content_title': content_ref.title,
                    'content_type': content_ref.content_type,
                    'total_lines': content_ref.total_lines,
                    'requested_range': {
                        'start_line': start_line,
                        'end_line': end_line
                    },
                    'actual_range': {
                        'start_line': start_line,
                        'end_line': actual_end_line
                    },
                    'lines_returned': len(lines),
                    'content': formatted_content,
                    'raw_content': content_range
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error viewing content range: {str(e)}"
            )
