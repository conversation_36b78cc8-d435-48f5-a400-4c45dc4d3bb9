"""
Tests for Augment Agent Replica
"""

import pytest
import asyncio
import tempfile
import os
from unittest.mock import Mock, patch

from src.core.agent import AugmentAgentReplica
from src.core.tool_executor import Tool<PERSON>xecutor, ToolResult
from src.tools.file_management.view_tool import ViewTool
from src.utils.config import Config

@pytest.fixture
def temp_workspace():
    """Create a temporary workspace for testing"""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir

@pytest.fixture
def test_config(temp_workspace):
    """Create test configuration"""
    config = Config()
    config.workspace_root = temp_workspace
    config.mistral_api_key = "test_key"
    return config

@pytest.fixture
def mock_mistral_client():
    """Mock Mistral AI client"""
    with patch('src.core.agent.MistralAIClient') as mock:
        mock_instance = Mock()
        mock_instance.chat_completion.return_value = {
            'content': 'Test response',
            'tool_calls': None
        }
        mock.return_value = mock_instance
        yield mock_instance

@pytest.fixture
def agent(test_config, mock_mistral_client):
    """Create test agent instance"""
    return AugmentAgentReplica(test_config)

class TestToolExecutor:
    """Test the tool execution framework"""
    
    def test_tool_registration(self):
        """Test tool registration"""
        executor = ToolExecutor()
        tool = ViewTool()
        
        executor.register_tool(tool)
        
        assert 'view' in executor.tools
        assert executor.tools['view'] == tool
    
    def test_list_tools(self):
        """Test listing tools"""
        executor = ToolExecutor()
        tool = ViewTool()
        executor.register_tool(tool)
        
        tools = executor.list_tools()
        
        assert len(tools) == 1
        assert tools[0]['name'] == 'view'
        assert tools[0]['category'] == 'file_management'
    
    @pytest.mark.asyncio
    async def test_tool_execution_success(self, temp_workspace):
        """Test successful tool execution"""
        executor = ToolExecutor()
        tool = ViewTool()
        executor.register_tool(tool)
        
        # Create test file
        test_file = os.path.join(temp_workspace, 'test.txt')
        with open(test_file, 'w') as f:
            f.write('Hello, World!')
        
        # Execute tool
        result = await executor.execute_tool(
            'view',
            {
                'path': 'test.txt',
                'type': 'file',
                '_context': {'workspace_root': temp_workspace}
            }
        )
        
        assert result.success
        assert 'Hello, World!' in result.data['content']
    
    @pytest.mark.asyncio
    async def test_tool_execution_failure(self):
        """Test tool execution failure"""
        executor = ToolExecutor()
        
        # Try to execute non-existent tool
        result = await executor.execute_tool('nonexistent', {})
        
        assert not result.success
        assert 'not found' in result.error

class TestViewTool:
    """Test the view tool"""
    
    @pytest.mark.asyncio
    async def test_view_file(self, temp_workspace):
        """Test viewing a file"""
        tool = ViewTool()
        
        # Create test file
        test_file = os.path.join(temp_workspace, 'test.py')
        with open(test_file, 'w') as f:
            f.write('def hello():\n    print("Hello, World!")\n')
        
        # View file
        result = await tool.execute(
            path='test.py',
            type='file',
            _context={'workspace_root': temp_workspace}
        )
        
        assert result.success
        assert 'def hello():' in result.data['content']
        assert result.data['type'] == 'file'
    
    @pytest.mark.asyncio
    async def test_view_directory(self, temp_workspace):
        """Test viewing a directory"""
        tool = ViewTool()
        
        # Create test files
        os.makedirs(os.path.join(temp_workspace, 'subdir'))
        with open(os.path.join(temp_workspace, 'file1.txt'), 'w') as f:
            f.write('content1')
        with open(os.path.join(temp_workspace, 'subdir', 'file2.txt'), 'w') as f:
            f.write('content2')
        
        # View directory
        result = await tool.execute(
            path='.',
            type='directory',
            _context={'workspace_root': temp_workspace}
        )
        
        assert result.success
        assert 'file1.txt' in result.data['content']
        assert 'subdir' in result.data['content']
    
    @pytest.mark.asyncio
    async def test_regex_search(self, temp_workspace):
        """Test regex search in file"""
        tool = ViewTool()
        
        # Create test file with multiple lines
        test_file = os.path.join(temp_workspace, 'test.py')
        with open(test_file, 'w') as f:
            f.write('''def function1():
    pass

class MyClass:
    def method1(self):
        pass
    
    def method2(self):
        pass

def function2():
    pass
''')
        
        # Search for functions
        result = await tool.execute(
            path='test.py',
            type='file',
            search_query_regex='def.*:',
            _context={'workspace_root': temp_workspace}
        )
        
        assert result.success
        assert result.data['matches_found'] == 4  # function1, method1, method2, function2
        assert 'def function1():' in result.data['content']

class TestAugmentAgentReplica:
    """Test the main agent class"""
    
    def test_agent_initialization(self, agent):
        """Test agent initialization"""
        assert agent is not None
        assert len(agent.tool_executor.tools) > 0
        assert 'view' in agent.tool_executor.tools
    
    def test_get_available_tools(self, agent):
        """Test getting available tools"""
        tools = agent.get_available_tools()
        
        assert len(tools) > 0
        tool_names = [tool['name'] for tool in tools]
        assert 'view' in tool_names
    
    def test_get_agent_status(self, agent):
        """Test getting agent status"""
        status = agent.get_agent_status()
        
        assert status['status'] == 'active'
        assert status['tools_registered'] > 0
        assert 'execution_stats' in status
        assert 'model_info' in status
    
    @pytest.mark.asyncio
    async def test_execute_tool_directly(self, agent, temp_workspace):
        """Test direct tool execution"""
        # Create test file
        test_file = os.path.join(temp_workspace, 'direct_test.txt')
        with open(test_file, 'w') as f:
            f.write('Direct execution test')
        
        # Execute tool directly
        result = await agent.execute_tool_directly(
            'view',
            {
                'path': 'direct_test.txt',
                'type': 'file'
            }
        )
        
        assert result['success']
        assert 'Direct execution test' in result['data']['content']
    
    @pytest.mark.asyncio
    async def test_chat_basic(self, agent, mock_mistral_client):
        """Test basic chat functionality"""
        # Mock the analysis response
        mock_mistral_client.chat_completion.side_effect = [
            # Analysis response
            {'content': '{"complexity": "simple", "approach": "direct_response", "requires_code_changes": false}'},
            # Main response
            {'content': 'Hello! How can I help you?', 'tool_calls': None}
        ]
        
        responses = []
        async for chunk in agent.chat("Hello"):
            responses.append(chunk)
        
        assert len(responses) > 0
        assert any(chunk['type'] == 'content' for chunk in responses)

if __name__ == '__main__':
    pytest.main([__file__])
