# Mistral AI Configuration
MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_MODEL=mistral-large-latest
MISTRAL_MAX_TOKENS=4096
MISTRAL_TEMPERATURE=0.1

# Alternative AI Models (for compatibility)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Web Search Configuration
GOOGLE_SEARCH_API_KEY=your_google_search_api_key
GOOGLE_SEARCH_ENGINE_ID=your_custom_search_engine_id
BING_SEARCH_API_KEY=your_bing_search_api_key

# Database Configuration
DATABASE_URL=sqlite:///./agent.db
REDIS_URL=redis://localhost:6379/0

# Server Configuration
HOST=localhost
PORT=8000
DEBUG=true
CORS_ORIGINS=http://localhost:3000,http://localhost:8000

# Security
SECRET_KEY=your_secret_key_here_change_in_production
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# File Management
MAX_FILE_SIZE=100MB
WORKSPACE_ROOT=./workspace
TEMP_DIR=./temp
BACKUP_DIR=./backups

# Process Management
MAX_PROCESSES=10
PROCESS_TIMEOUT=300
TERMINAL_HISTORY_SIZE=10000

# Code Intelligence
CODEBASE_INDEX_PATH=./indexes/codebase
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
MAX_SEARCH_RESULTS=50

# Task Management
TASK_DB_PATH=./data/tasks.db
MAX_TASKS_PER_PROJECT=1000
TASK_RETENTION_DAYS=90

# Memory System
MEMORY_DB_PATH=./data/memory.db
MEMORY_RETENTION_DAYS=365
MAX_MEMORIES=10000

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/agent.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5

# Performance
CACHE_TTL=3600
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=30

# Web Interface
STATIC_FILES_PATH=./src/web/static
TEMPLATES_PATH=./src/web/templates
UPLOAD_PATH=./uploads

# Development
RELOAD=true
WORKERS=1
ACCESS_LOG=true

# Monitoring
PROMETHEUS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# External Services
GITHUB_TOKEN=your_github_token_here
GITLAB_TOKEN=your_gitlab_token_here
DOCKER_HOST=unix:///var/run/docker.sock

# Browser Automation
SELENIUM_DRIVER_PATH=./drivers/chromedriver
HEADLESS_BROWSER=true
BROWSER_TIMEOUT=30

# Diagram Generation
MERMAID_CONFIG_PATH=./config/mermaid.json
GRAPHVIZ_PATH=/usr/bin/dot

# Backup and Recovery
AUTO_BACKUP=true
BACKUP_INTERVAL=24h
MAX_BACKUPS=30
