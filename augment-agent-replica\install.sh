#!/bin/bash

# Augment Agent Replica Installation Script
# This script sets up the complete environment for running the agent

set -e  # Exit on any error

echo "🤖 Augment Agent Replica - Installation Script"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python 3.9+ is installed
check_python() {
    print_status "Checking Python version..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
        
        if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 9 ]; then
            print_success "Python $PYTHON_VERSION found"
            PYTHON_CMD="python3"
        else
            print_error "Python 3.9+ required, found $PYTHON_VERSION"
            exit 1
        fi
    else
        print_error "Python 3 not found. Please install Python 3.9 or higher."
        exit 1
    fi
}

# Check if Node.js is installed
check_nodejs() {
    print_status "Checking Node.js version..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version | cut -d'v' -f2)
        NODE_MAJOR=$(echo $NODE_VERSION | cut -d. -f1)
        
        if [ "$NODE_MAJOR" -ge 16 ]; then
            print_success "Node.js $NODE_VERSION found"
        else
            print_warning "Node.js 16+ recommended, found $NODE_VERSION"
        fi
    else
        print_warning "Node.js not found. Some features may not work."
        print_status "Install Node.js from: https://nodejs.org/"
    fi
}

# Create virtual environment
create_venv() {
    print_status "Creating Python virtual environment..."
    
    if [ ! -d "venv" ]; then
        $PYTHON_CMD -m venv venv
        print_success "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
    fi
}

# Activate virtual environment
activate_venv() {
    print_status "Activating virtual environment..."
    
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
        print_success "Virtual environment activated"
    elif [ -f "venv/Scripts/activate" ]; then
        source venv/Scripts/activate
        print_success "Virtual environment activated"
    else
        print_error "Could not find virtual environment activation script"
        exit 1
    fi
}

# Install Python dependencies
install_python_deps() {
    print_status "Installing Python dependencies..."
    
    pip install --upgrade pip
    pip install -r requirements.txt
    
    print_success "Python dependencies installed"
}

# Install Node.js dependencies
install_nodejs_deps() {
    if command -v npm &> /dev/null; then
        print_status "Installing Node.js dependencies..."
        npm install
        print_success "Node.js dependencies installed"
    else
        print_warning "npm not found, skipping Node.js dependencies"
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p data
    mkdir -p workspace
    mkdir -p temp
    mkdir -p backups
    mkdir -p uploads
    mkdir -p indexes/codebase
    
    print_success "Directories created"
}

# Copy environment file
setup_env() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_success "Environment file created from template"
        print_warning "Please edit .env file and add your API keys"
    else
        print_warning ".env file already exists"
    fi
}

# Install package in development mode
install_package() {
    print_status "Installing package in development mode..."
    
    pip install -e .
    
    print_success "Package installed"
}

# Run tests
run_tests() {
    if [ "$1" = "--skip-tests" ]; then
        print_warning "Skipping tests"
        return
    fi
    
    print_status "Running tests..."
    
    if command -v pytest &> /dev/null; then
        pytest tests/ -v || print_warning "Some tests failed"
    else
        print_warning "pytest not found, skipping tests"
    fi
}

# Main installation function
main() {
    echo
    print_status "Starting installation process..."
    echo
    
    # Check prerequisites
    check_python
    check_nodejs
    
    # Setup Python environment
    create_venv
    activate_venv
    install_python_deps
    
    # Setup Node.js environment
    install_nodejs_deps
    
    # Setup project
    create_directories
    setup_env
    install_package
    
    # Run tests
    run_tests "$1"
    
    echo
    print_success "Installation completed successfully!"
    echo
    echo "🚀 Next steps:"
    echo "1. Edit .env file and add your Mistral AI API key"
    echo "2. Run: source venv/bin/activate (or venv\\Scripts\\activate on Windows)"
    echo "3. Start the agent:"
    echo "   - Interactive mode: python src/main.py --interactive"
    echo "   - Web interface: python src/main.py --server"
    echo "   - Single command: python src/main.py --command 'view README.md'"
    echo
    echo "📚 Documentation: See README.md for detailed usage instructions"
    echo "🐛 Issues: Report bugs at https://github.com/your-username/augment-agent-replica/issues"
    echo
}

# Handle command line arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Augment Agent Replica Installation Script"
    echo
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  --help, -h     Show this help message"
    echo "  --skip-tests   Skip running tests during installation"
    echo
    echo "This script will:"
    echo "  1. Check Python 3.9+ and Node.js 16+ installation"
    echo "  2. Create Python virtual environment"
    echo "  3. Install Python and Node.js dependencies"
    echo "  4. Create necessary directories"
    echo "  5. Setup environment configuration"
    echo "  6. Install the package in development mode"
    echo "  7. Run tests (optional)"
    echo
    exit 0
fi

# Run main installation
main "$1"
