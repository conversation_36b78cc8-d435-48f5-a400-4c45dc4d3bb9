"""
Save File Tool - Exact replica of Augment Agent's save-file functionality
Creates new files with content validation and size limits
"""

import os
from typing import Dict, Any
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult

class SaveFileTool(BaseTool):
    """
    Tool for creating new files with content
    Exact replica of original Augment Agent save-file tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.FILE_MANAGEMENT
    
    def _get_description(self) -> str:
        return """Save a new file. Use this tool to write new files with the attached content. Generate `instructions_reminder` first to remind yourself to limit the file content to at most 300 lines. It CANNOT modify existing files. Do NOT use this tool to edit an existing file by overwriting it entirely. Use the str-replace-editor tool to edit existing files instead."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "instructions_reminder": {
                    "description": "Should be exactly this string: 'LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.'",
                    "type": "string"
                },
                "path": {
                    "description": "The path of the file to save.",
                    "type": "string"
                },
                "file_content": {
                    "description": "The content of the file.",
                    "type": "string"
                },
                "add_last_line_newline": {
                    "description": "Whether to add a newline at the end of the file (default: true).",
                    "type": "boolean",
                    "default": True
                }
            },
            "required": ["instructions_reminder", "path", "file_content"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the save-file tool"""
        try:
            instructions_reminder = kwargs.get('instructions_reminder', '')
            path = kwargs.get('path', '')
            file_content = kwargs.get('file_content', '')
            add_last_line_newline = kwargs.get('add_last_line_newline', True)
            
            # Validate instructions reminder
            expected_reminder = 'LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.'
            if instructions_reminder != expected_reminder:
                return ToolResult(
                    success=False,
                    error=f"Invalid instructions_reminder. Expected: '{expected_reminder}'"
                )
            
            # Get workspace root from context
            workspace_root = kwargs.get('_context', {}).get('workspace_root', os.getcwd())
            full_path = os.path.join(workspace_root, path)
            
            # Check if file already exists
            if os.path.exists(full_path):
                return ToolResult(
                    success=False,
                    error=f"File already exists: {path}. Use str-replace-editor to modify existing files."
                )
            
            # Validate content length (300 lines max)
            lines = file_content.split('\n')
            if len(lines) > 300:
                return ToolResult(
                    success=False,
                    error=f"File content exceeds 300 lines ({len(lines)} lines). Please reduce content or use str-replace-editor to add more content after creation."
                )
            
            # Create directory if it doesn't exist
            directory = os.path.dirname(full_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
            
            # Prepare content
            content_to_write = file_content
            if add_last_line_newline and not content_to_write.endswith('\n'):
                content_to_write += '\n'
            
            # Write file
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content_to_write)
            
            # Get file stats
            file_size = os.path.getsize(full_path)
            
            return ToolResult(
                success=True,
                data={
                    'operation': 'save_file',
                    'file_path': full_path,
                    'relative_path': path,
                    'lines_written': len(lines),
                    'file_size_bytes': file_size,
                    'newline_added': add_last_line_newline and not file_content.endswith('\n')
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error saving file: {str(e)}"
            )
