"""
Add Tasks Tool - Exact replica of Augment Agent's add_tasks functionality
Add one or more new tasks to the task list
"""

from typing import Dict, Any, List, Optional
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult
from .task_manager import task_manager, TaskState

class AddTasksTool(BaseTool):
    """
    Add one or more new tasks to the task list
    Exact replica of original Augment Agent add_tasks tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.TASK_MANAGEMENT
    
    def _get_description(self) -> str:
        return """Add one or more new tasks to the task list. Can add a single task or multiple tasks in one call. Tasks can be added as subtasks or after specific tasks. Use this when planning complex sequences of work."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                # Single task properties
                "name": {
                    "description": "The name of the new task for single task creation.",
                    "type": "string"
                },
                "description": {
                    "description": "The description of the new task for single task creation.",
                    "type": "string"
                },
                "parent_task_id": {
                    "description": "UUID of the parent task if this should be a subtask for single task creation.",
                    "type": "string"
                },
                "after_task_id": {
                    "description": "UUID of the task after which this task should be inserted for single task creation.",
                    "type": "string"
                },
                "state": {
                    "description": "Initial state of the task for single task creation. Defaults to NOT_STARTED.",
                    "type": "string",
                    "enum": ["NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE"],
                    "default": "NOT_STARTED"
                },
                # Multiple tasks
                "tasks": {
                    "description": "Array of tasks to create. Use this for multiple task creation. Each task should have name and description. Either single task properties or tasks array is required.",
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {
                                "description": "The name of the new task.",
                                "type": "string"
                            },
                            "description": {
                                "description": "The description of the new task.",
                                "type": "string"
                            },
                            "parent_task_id": {
                                "description": "UUID of the parent task if this should be a subtask.",
                                "type": "string"
                            },
                            "after_task_id": {
                                "description": "UUID of the task after which this task should be inserted.",
                                "type": "string"
                            },
                            "state": {
                                "description": "Initial state of the task. Defaults to NOT_STARTED.",
                                "type": "string",
                                "enum": ["NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE"],
                                "default": "NOT_STARTED"
                            }
                        },
                        "required": ["name", "description"]
                    }
                }
            }
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the add_tasks tool"""
        try:
            # Get conversation ID from context if available
            conversation_id = kwargs.get('_context', {}).get('conversation_id', 'default')
            task_manager.set_conversation_id(conversation_id)
            
            # Determine if single task or multiple tasks
            tasks_to_add = []
            
            if 'tasks' in kwargs:
                # Multiple tasks mode
                tasks_data = kwargs['tasks']
                if not isinstance(tasks_data, list):
                    return ToolResult(
                        success=False,
                        error="tasks parameter must be an array"
                    )
                
                for task_data in tasks_data:
                    if not isinstance(task_data, dict):
                        return ToolResult(
                            success=False,
                            error="Each task in tasks array must be an object"
                        )
                    
                    if 'name' not in task_data or 'description' not in task_data:
                        return ToolResult(
                            success=False,
                            error="Each task must have 'name' and 'description' properties"
                        )
                    
                    tasks_to_add.append(task_data)
            
            else:
                # Single task mode
                name = kwargs.get('name')
                description = kwargs.get('description')
                
                if not name or not description:
                    return ToolResult(
                        success=False,
                        error="Either 'tasks' array or both 'name' and 'description' are required"
                    )
                
                tasks_to_add.append({
                    'name': name,
                    'description': description,
                    'parent_task_id': kwargs.get('parent_task_id'),
                    'after_task_id': kwargs.get('after_task_id'),
                    'state': kwargs.get('state', 'NOT_STARTED')
                })
            
            # Add all tasks
            created_tasks = []
            for task_data in tasks_to_add:
                try:
                    # Parse state
                    state_str = task_data.get('state', 'NOT_STARTED')
                    state = TaskState(state_str)
                    
                    # Add the task
                    task = task_manager.add_task(
                        name=task_data['name'],
                        description=task_data['description'],
                        parent_task_id=task_data.get('parent_task_id'),
                        after_task_id=task_data.get('after_task_id'),
                        state=state
                    )
                    
                    created_tasks.append(task.to_dict())
                
                except ValueError as e:
                    return ToolResult(
                        success=False,
                        error=f"Invalid state value: {task_data.get('state')}. Must be one of: NOT_STARTED, IN_PROGRESS, COMPLETE, CANCELLED"
                    )
                except Exception as e:
                    return ToolResult(
                        success=False,
                        error=f"Error creating task '{task_data['name']}': {str(e)}"
                    )
            
            # Get updated task list summary
            all_tasks = task_manager.get_tasks()
            summary = {
                'total_tasks': len(all_tasks),
                'tasks_created': len(created_tasks),
                'not_started': len([t for t in all_tasks if t.state.value == 'NOT_STARTED']),
                'in_progress': len([t for t in all_tasks if t.state.value == 'IN_PROGRESS']),
                'complete': len([t for t in all_tasks if t.state.value == 'COMPLETE']),
                'cancelled': len([t for t in all_tasks if t.state.value == 'CANCELLED'])
            }
            
            return ToolResult(
                success=True,
                data={
                    'conversation_id': conversation_id,
                    'created_tasks': created_tasks,
                    'summary': summary,
                    'message': f"Successfully created {len(created_tasks)} task(s)"
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error adding tasks: {str(e)}"
            )
