"""
Web Search Tool - Exact replica of Augment Agent's web-search functionality
Search the web without requiring API keys using DuckDuckGo and other free sources
"""

import requests
import re
import json
import urllib.parse
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import time
import logging

from ...core.tool_executor import BaseTool, ToolCategory, ToolResult

logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    """Represents a web search result"""
    title: str
    url: str
    snippet: str
    source: str = 'web'

class WebSearchTool(BaseTool):
    """
    Search the web for information without requiring API keys
    Exact replica of original Augment Agent web-search tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.WEB_INTEGRATION
    
    def _get_description(self) -> str:
        return """Search the web for information. Returns results in markdown format.
Each result includes the URL, title, and a snippet from the page if available.

This tool uses multiple free search sources to find relevant web pages."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "query": {
                    "description": "The search query to send.",
                    "type": "string"
                },
                "num_results": {
                    "description": "Number of results to return",
                    "type": "integer",
                    "default": 5,
                    "minimum": 1,
                    "maximum": 10
                }
            },
            "required": ["query"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute web search"""
        try:
            query = kwargs.get('query', '')
            num_results = kwargs.get('num_results', 5)
            
            if not query:
                return ToolResult(
                    success=False,
                    error="query parameter is required"
                )
            
            # Validate num_results
            if not isinstance(num_results, int) or num_results < 1 or num_results > 10:
                num_results = 5
            
            # Perform search using multiple sources
            results = []
            
            # Try DuckDuckGo first (no API key required)
            try:
                ddg_results = await self._search_duckduckgo(query, num_results)
                results.extend(ddg_results)
            except Exception as e:
                logger.warning(f"DuckDuckGo search failed: {e}")
            
            # If we don't have enough results, try other sources
            if len(results) < num_results:
                try:
                    bing_results = await self._search_bing_scrape(query, num_results - len(results))
                    results.extend(bing_results)
                except Exception as e:
                    logger.warning(f"Bing scrape search failed: {e}")
            
            # If still not enough, try Google scrape (be careful with rate limits)
            if len(results) < num_results:
                try:
                    google_results = await self._search_google_scrape(query, num_results - len(results))
                    results.extend(google_results)
                except Exception as e:
                    logger.warning(f"Google scrape search failed: {e}")
            
            # Limit to requested number of results
            results = results[:num_results]
            
            # Format results as markdown
            markdown_results = self._format_results_as_markdown(results, query)
            
            return ToolResult(
                success=True,
                data={
                    'query': query,
                    'num_results_requested': num_results,
                    'num_results_found': len(results),
                    'results': [
                        {
                            'title': result.title,
                            'url': result.url,
                            'snippet': result.snippet,
                            'source': result.source
                        }
                        for result in results
                    ],
                    'markdown': markdown_results
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error in web search: {str(e)}"
            )
    
    async def _search_duckduckgo(self, query: str, num_results: int) -> List[SearchResult]:
        """Search using DuckDuckGo instant answer API"""
        results = []
        
        try:
            # DuckDuckGo instant answer API
            url = "https://api.duckduckgo.com/"
            params = {
                'q': query,
                'format': 'json',
                'no_html': '1',
                'skip_disambig': '1'
            }
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            # Extract results from different sections
            if data.get('AbstractText'):
                results.append(SearchResult(
                    title=data.get('AbstractSource', 'DuckDuckGo'),
                    url=data.get('AbstractURL', ''),
                    snippet=data.get('AbstractText', ''),
                    source='duckduckgo'
                ))
            
            # Related topics
            for topic in data.get('RelatedTopics', [])[:num_results]:
                if isinstance(topic, dict) and topic.get('Text'):
                    results.append(SearchResult(
                        title=topic.get('Text', '').split(' - ')[0],
                        url=topic.get('FirstURL', ''),
                        snippet=topic.get('Text', ''),
                        source='duckduckgo'
                    ))
            
            # If we need more results, try the HTML search
            if len(results) < num_results:
                html_results = await self._search_duckduckgo_html(query, num_results - len(results))
                results.extend(html_results)
        
        except Exception as e:
            logger.error(f"DuckDuckGo API search failed: {e}")
        
        return results[:num_results]
    
    async def _search_duckduckgo_html(self, query: str, num_results: int) -> List[SearchResult]:
        """Search DuckDuckGo HTML interface"""
        results = []
        
        try:
            url = "https://html.duckduckgo.com/html/"
            params = {'q': query}
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            # Parse HTML results using regex (simple parsing)
            html = response.text
            
            # Find result blocks
            result_pattern = r'<div class="result__body">.*?<a rel="nofollow" href="([^"]+)".*?<h2 class="result__title">.*?>(.*?)</a>.*?<a class="result__snippet".*?>(.*?)</a>'
            matches = re.findall(result_pattern, html, re.DOTALL | re.IGNORECASE)
            
            for match in matches[:num_results]:
                url_match, title_match, snippet_match = match
                
                # Clean up the extracted text
                title = re.sub(r'<[^>]+>', '', title_match).strip()
                snippet = re.sub(r'<[^>]+>', '', snippet_match).strip()
                
                if title and url_match:
                    results.append(SearchResult(
                        title=title,
                        url=url_match,
                        snippet=snippet,
                        source='duckduckgo'
                    ))
        
        except Exception as e:
            logger.error(f"DuckDuckGo HTML search failed: {e}")
        
        return results
    
    async def _search_bing_scrape(self, query: str, num_results: int) -> List[SearchResult]:
        """Search Bing by scraping (use carefully to avoid rate limits)"""
        results = []
        
        try:
            url = "https://www.bing.com/search"
            params = {'q': query, 'count': num_results}
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            html = response.text
            
            # Parse Bing results using regex
            result_pattern = r'<li class="b_algo">.*?<h2><a href="([^"]+)".*?>(.*?)</a></h2>.*?<div class="b_caption">.*?<p>(.*?)</p>'
            matches = re.findall(result_pattern, html, re.DOTALL | re.IGNORECASE)
            
            for match in matches[:num_results]:
                url_match, title_match, snippet_match = match
                
                # Clean up the extracted text
                title = re.sub(r'<[^>]+>', '', title_match).strip()
                snippet = re.sub(r'<[^>]+>', '', snippet_match).strip()
                
                if title and url_match:
                    results.append(SearchResult(
                        title=title,
                        url=url_match,
                        snippet=snippet,
                        source='bing'
                    ))
        
        except Exception as e:
            logger.error(f"Bing scrape search failed: {e}")
        
        return results
    
    async def _search_google_scrape(self, query: str, num_results: int) -> List[SearchResult]:
        """Search Google by scraping (use very carefully to avoid blocking)"""
        results = []
        
        try:
            # Add delay to be respectful
            time.sleep(1)
            
            url = "https://www.google.com/search"
            params = {'q': query, 'num': num_results}
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            html = response.text
            
            # Parse Google results using regex (Google's HTML structure changes frequently)
            # This is a simplified pattern that may need updates
            result_pattern = r'<div class="g">.*?<a href="([^"]+)".*?<h3.*?>(.*?)</h3>.*?<span.*?>(.*?)</span>'
            matches = re.findall(result_pattern, html, re.DOTALL | re.IGNORECASE)
            
            for match in matches[:num_results]:
                url_match, title_match, snippet_match = match
                
                # Skip Google's internal URLs
                if url_match.startswith('/url?q='):
                    url_match = urllib.parse.unquote(url_match.split('&')[0][7:])
                
                # Clean up the extracted text
                title = re.sub(r'<[^>]+>', '', title_match).strip()
                snippet = re.sub(r'<[^>]+>', '', snippet_match).strip()
                
                if title and url_match and not url_match.startswith('http://google.com'):
                    results.append(SearchResult(
                        title=title,
                        url=url_match,
                        snippet=snippet,
                        source='google'
                    ))
        
        except Exception as e:
            logger.error(f"Google scrape search failed: {e}")
        
        return results
    
    def _format_results_as_markdown(self, results: List[SearchResult], query: str) -> str:
        """Format search results as markdown"""
        if not results:
            return f"No results found for query: {query}"
        
        markdown = f"# Search Results for: {query}\n\n"
        
        for i, result in enumerate(results, 1):
            markdown += f"## {i}. {result.title}\n"
            markdown += f"**URL:** {result.url}\n"
            if result.snippet:
                markdown += f"**Snippet:** {result.snippet}\n"
            markdown += f"**Source:** {result.source}\n\n"
        
        return markdown
