"""
Mistral AI Client for Augment Agent Replica
Handles all interactions with Mistral AI API
"""

import os
import json
import asyncio
from typing import Dict, List, Any, Optional, AsyncGenerator
import logging

# Handle different versions of Mistral AI library
try:
    # Try newer version first
    from mistralai import Mistral
    from mistralai.models import UserMessage, SystemMessage, AssistantMessage
    MISTRAL_NEW_API = True

    # Create a compatibility layer
    class ChatMessage:
        def __init__(self, role: str, content: str):
            self.role = role
            self.content = content

except ImportError:
    try:
        # Try older version
        from mistralai.client import MistralClient
        from mistralai.models.chat_completion import ChatMessage
        MISTRAL_NEW_API = False
    except ImportError:
        # Fallback for development without Mistral AI
        MISTRAL_NEW_API = False

        class ChatMessage:
            def __init__(self, role: str, content: str):
                self.role = role
                self.content = content

        class MistralClient:
            def __init__(self, api_key: str):
                self.api_key = api_key

            def chat(self, **kwargs):
                # Mock response for development
                class MockChoice:
                    def __init__(self):
                        self.message = type('obj', (object,), {
                            'content': 'Mock response - Mistral AI not available',
                            'tool_calls': None
                        })()

                class MockUsage:
                    def dict(self):
                        return {'prompt_tokens': 0, 'completion_tokens': 0}

                return type('obj', (object,), {
                    'choices': [MockChoice()],
                    'usage': MockUsage(),
                    'model': 'mock-model'
                })()

            def chat_stream(self, **kwargs):
                # Mock streaming response
                class MockDelta:
                    def __init__(self, content):
                        self.content = content
                        self.tool_calls = None

                class MockChoice:
                    def __init__(self, content):
                        self.delta = MockDelta(content)

                class MockChunk:
                    def __init__(self, content):
                        self.choices = [MockChoice(content)]

                yield MockChunk("Mock streaming response - Mistral AI not available")

logger = logging.getLogger(__name__)

class MistralAIClient:
    """Enhanced Mistral AI client with streaming and tool support"""
    
    def __init__(self):
        self.api_key = os.getenv('MISTRAL_API_KEY')
        if not self.api_key:
            logger.warning("MISTRAL_API_KEY environment variable not found - using mock client")
            self.api_key = "mock_key"

        # Initialize client based on available API version
        if MISTRAL_NEW_API and 'Mistral' in globals():
            self.client = Mistral(api_key=self.api_key)
        else:
            self.client = MistralClient(api_key=self.api_key)

        self.model = os.getenv('MISTRAL_MODEL', 'mistral-large-latest')
        self.max_tokens = int(os.getenv('MISTRAL_MAX_TOKENS', '4096'))
        self.temperature = float(os.getenv('MISTRAL_TEMPERATURE', '0.1'))
        
        # System prompt that replicates Augment Agent behavior
        self.system_prompt = self._load_system_prompt()
    
    def _load_system_prompt(self) -> str:
        """Load the comprehensive system prompt that replicates Augment Agent"""
        return """# Role
You are Augment Agent Replica, an agentic coding AI assistant with access to 25+ specialized tools for comprehensive software development workflows. You have the exact same capabilities and behavior patterns as the original Augment Agent.

# Identity
You are based on Mistral AI's latest model, enhanced with Augment's world-leading context engine and tool integrations. You can read from and write to codebases using the provided tools.

# Core Principles
1. **Information First**: Always gather context before making changes using codebase-retrieval
2. **Conservative Edits**: Respect existing codebase patterns and make precise changes
3. **Batch Operations**: Use efficient batch updates for tasks and processes
4. **Test-Driven**: Always suggest testing after code changes
5. **User Permission**: Ask before potentially destructive actions
6. **Progress Tracking**: Use task management for complex work

# Tool Usage Patterns
- Before ANY file edits, use codebase-retrieval to understand the context
- Use str-replace-editor for precise edits, never overwrite entire files
- Use task management tools for complex multi-step work
- Always use package managers instead of manual dependency file editing
- Batch task updates efficiently: mark current complete and next in progress

# Workflow Replication
1. **Information Gathering**: Use codebase-retrieval and view tools
2. **Planning**: Create structured task breakdowns for complex work
3. **Implementation**: Make precise, conservative edits
4. **Testing**: Run tests and validate changes
5. **Progress Tracking**: Update task states efficiently

# Code Display Standards
When showing code, always use:
<augment_code_snippet path="file/path" mode="EXCERPT">
````language
# Code content (max 10 lines)
````
</augment_code_snippet>

# Error Recovery
If you find yourself repeating similar tool calls without progress, ask the user for help.

You have access to all 25+ tools that the original Augment Agent has. Use them exactly as the original would."""

    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        tools: Optional[List[Dict]] = None,
        stream: bool = False
    ) -> Dict[str, Any]:
        """
        Generate chat completion with tool support
        """
        try:
            # Convert messages to Mistral format
            mistral_messages = [
                ChatMessage(role=msg["role"], content=msg["content"])
                for msg in messages
            ]
            
            # Add system message if not present
            if not any(msg.role == "system" for msg in mistral_messages):
                mistral_messages.insert(0, ChatMessage(
                    role="system", 
                    content=self.system_prompt
                ))
            
            if stream:
                return await self._stream_completion(mistral_messages, tools)
            else:
                return await self._regular_completion(mistral_messages, tools)
                
        except Exception as e:
            logger.error(f"Error in chat completion: {e}")
            raise
    
    async def _regular_completion(
        self, 
        messages: List[ChatMessage], 
        tools: Optional[List[Dict]] = None
    ) -> Dict[str, Any]:
        """Regular non-streaming completion"""
        
        response = self.client.chat(
            model=self.model,
            messages=messages,
            max_tokens=self.max_tokens,
            temperature=self.temperature,
            tools=tools if tools else None
        )
        
        return {
            "content": response.choices[0].message.content,
            "tool_calls": getattr(response.choices[0].message, 'tool_calls', None),
            "usage": response.usage.dict() if response.usage else None,
            "model": response.model
        }
    
    async def _stream_completion(
        self, 
        messages: List[ChatMessage], 
        tools: Optional[List[Dict]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Streaming completion"""
        
        stream = self.client.chat_stream(
            model=self.model,
            messages=messages,
            max_tokens=self.max_tokens,
            temperature=self.temperature,
            tools=tools if tools else None
        )
        
        for chunk in stream:
            if chunk.choices[0].delta.content:
                yield {
                    "content": chunk.choices[0].delta.content,
                    "type": "content"
                }
            
            if hasattr(chunk.choices[0].delta, 'tool_calls') and chunk.choices[0].delta.tool_calls:
                yield {
                    "tool_calls": chunk.choices[0].delta.tool_calls,
                    "type": "tool_calls"
                }
    
    def format_tool_definitions(self, tools: List[Any]) -> List[Dict]:
        """Format tool definitions for Mistral API"""
        formatted_tools = []
        
        for tool in tools:
            formatted_tool = {
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.parameters
                }
            }
            formatted_tools.append(formatted_tool)
        
        return formatted_tools
    
    async def analyze_code_context(self, code: str, language: str = "python") -> Dict[str, Any]:
        """Analyze code context for better understanding"""
        
        analysis_prompt = f"""
        Analyze this {language} code and provide:
        1. Main classes and functions
        2. Dependencies and imports
        3. Key patterns and architecture
        4. Potential issues or improvements
        
        Code:
        ```{language}
        {code}
        ```
        """
        
        messages = [
            ChatMessage(role="user", content=analysis_prompt)
        ]
        
        response = await self._regular_completion(messages)
        return {
            "analysis": response["content"],
            "language": language,
            "code_length": len(code)
        }
    
    async def generate_code_suggestions(
        self, 
        context: str, 
        requirements: str,
        language: str = "python"
    ) -> Dict[str, Any]:
        """Generate code suggestions based on context and requirements"""
        
        suggestion_prompt = f"""
        Given this context:
        {context}
        
        Generate {language} code that meets these requirements:
        {requirements}
        
        Provide:
        1. Complete implementation
        2. Explanation of approach
        3. Testing suggestions
        4. Integration notes
        """
        
        messages = [
            ChatMessage(role="user", content=suggestion_prompt)
        ]
        
        response = await self._regular_completion(messages)
        return {
            "suggestions": response["content"],
            "language": language,
            "context_considered": True
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model"""
        return {
            "model": self.model,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "provider": "Mistral AI",
            "capabilities": [
                "Code generation",
                "Code analysis", 
                "Tool calling",
                "Streaming responses",
                "Context understanding",
                "Multi-language support"
            ]
        }
