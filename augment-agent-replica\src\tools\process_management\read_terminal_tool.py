"""
Read Terminal Tool - Exact replica of Augment Agent's read-terminal functionality
Read output from the active or most-recently used VSCode terminal
"""

from typing import Dict, Any
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult
from .process_manager import process_manager

class ReadTerminalTool(BaseTool):
    """
    Read output from the active or most-recently used VSCode terminal
    Exact replica of original Augment Agent read-terminal tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.PROCESS_MANAGEMENT
    
    def _get_description(self) -> str:
        return """Read output from the active or most-recently used VSCode terminal.

By default, it reads all of the text visible in the terminal, not just the output of the most recent command.

If you want to read only the selected text in the terminal, set `only_selected=true` in the tool input.
Only do this if you know the user has selected text that you want to read.

Note that this is unrelated to the list-processes and read-process tools, which interact with processes that were launched with the "launch-process" tool."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "only_selected": {
                    "description": "Whether to read only the selected text in the terminal.",
                    "type": "boolean",
                    "default": False
                }
            },
            "required": []
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the read-terminal tool"""
        try:
            only_selected = kwargs.get('only_selected', False)
            
            # Read from the active terminal
            terminal_output = process_manager.read_terminal(only_selected=only_selected)
            
            return ToolResult(
                success=True,
                data={
                    'terminal_output': terminal_output,
                    'only_selected': only_selected,
                    'active_terminal_id': process_manager.active_terminal_id
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error reading terminal: {str(e)}"
            )
