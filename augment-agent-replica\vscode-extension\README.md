# Augment Agent Replica - VSCode Extension

🤖 **Complete AI coding assistant with chat panel - exact replica of Augment Agent functionality**

## Features

### 💬 **Interactive Chat Panel**
- Real-time conversation with AI assistant
- Streaming responses with typing indicators
- Context-aware suggestions based on your workspace
- Beautiful, VSCode-themed interface

### 🛠️ **All 23 Tools Available**
- **File Management**: View, edit, create, and delete files
- **Code Intelligence**: Semantic search and diagnostics
- **Process Management**: Run commands and manage terminals
- **Web Integration**: Search web and fetch content
- **Task Management**: Create and manage project tasks
- **Memory System**: Remember important information
- **Content Analysis**: Deep content search and analysis

### 🎯 **Smart Context Awareness**
- Automatically detects your current workspace
- Understands active file and selected text
- Provides relevant suggestions based on context
- Maintains conversation history per workspace

### ⚡ **Quick Actions**
- Right-click on files to view or edit with agent
- Context menu integration for instant access
- Keyboard shortcuts for common actions
- Command palette integration

## Installation

### Option 1: From VSCode Marketplace (Coming Soon)
1. Open VSCode
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "Augment Agent Replica"
4. Click Install

### Option 2: Manual Installation
1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd augment-agent-replica/vscode-extension
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Compile the extension:
   ```bash
   npm run compile
   ```

4. Install the extension:
   ```bash
   code --install-extension augment-agent-replica-1.0.0.vsix
   ```

## Setup

### 1. Configure Mistral AI API Key
1. Open VSCode Settings (Ctrl+,)
2. Search for "Augment Agent"
3. Enter your Mistral AI API key in the "Mistral Api Key" field

### 2. Install Agent Server
The extension requires the Augment Agent Replica server to be installed:

```bash
# Navigate to the main project directory
cd augment-agent-replica

# Install dependencies
python install_dependencies.py

# Or manually
pip install -r requirements.txt
```

## Usage

### 🚀 **Getting Started**
1. **Open Chat Panel**: Press `Ctrl+Shift+A` or click the robot icon in the activity bar
2. **Start Chatting**: Type your questions or commands in the chat input
3. **Use Context**: Right-click on files for quick actions

### 💬 **Chat Commands**
- **View files**: "view README.md", "show me the main.py file"
- **Edit files**: "fix the bug in line 25", "add error handling to this function"
- **Search code**: "find all authentication functions", "search for API endpoints"
- **Get diagnostics**: "check for errors in this file", "analyze code quality"
- **Manage tasks**: "create a task list for this project", "mark task as complete"
- **Web search**: "search for Python best practices", "find React documentation"
- **Run commands**: "run tests", "start the development server"

### ⌨️ **Keyboard Shortcuts**
- `Ctrl+Shift+A`: Open chat panel
- `Ctrl+Shift+E`: Execute command
- `Ctrl+Shift+S`: Search codebase
- `Ctrl+Shift+T`: Manage tasks

### 🖱️ **Context Menu Actions**
- **Right-click on files**: View or edit with agent
- **Right-click in editor**: Get diagnostics, explain code
- **Right-click on folders**: Analyze directory structure

## Configuration

### Settings
Access settings via `File > Preferences > Settings` and search for "Augment Agent":

- **Mistral API Key**: Your Mistral AI API key (required)
- **Model**: AI model to use (default: mistral-large-latest)
- **Max Tokens**: Maximum tokens for responses (default: 4096)
- **Temperature**: Response creativity (default: 0.1)
- **Auto Index**: Automatically index codebase (default: true)
- **Show Diagnostics**: Show diagnostics in chat (default: true)

### Workspace Configuration
Create `.vscode/settings.json` in your workspace:

```json
{
  "augmentAgent.mistralApiKey": "your-api-key-here",
  "augmentAgent.model": "mistral-large-latest",
  "augmentAgent.autoIndex": true
}
```

## Features in Detail

### 📁 **File Management**
- **View**: Display file contents with syntax highlighting
- **Edit**: Make precise changes with line-by-line editing
- **Create**: Generate new files with proper structure
- **Delete**: Safely remove files with backup options

### 🧠 **Code Intelligence**
- **Semantic Search**: Find code by meaning, not just text
- **Real-time Indexing**: Always up-to-date code understanding
- **Cross-language Support**: Works with Python, JavaScript, TypeScript, and more
- **Diagnostics**: Detect errors, warnings, and code quality issues

### ⚙️ **Process Management**
- **Command Execution**: Run shell commands and scripts
- **Terminal Management**: Manage multiple terminal sessions
- **Background Processes**: Start servers and long-running tasks
- **Output Streaming**: Real-time command output

### 🌐 **Web Integration**
- **Smart Search**: Find relevant information without API keys
- **Content Extraction**: Convert web pages to readable format
- **Browser Automation**: Open relevant documentation

### 📋 **Task Management**
- **Project Planning**: Create comprehensive task lists
- **Progress Tracking**: Monitor task completion
- **Hierarchical Tasks**: Organize with subtasks and dependencies
- **Team Collaboration**: Share task lists and updates

## Troubleshooting

### Common Issues

**Extension not loading**
- Ensure VSCode version 1.74.0 or higher
- Check the Output panel for error messages
- Restart VSCode after installation

**Agent server not starting**
- Verify Python 3.9+ is installed
- Check that dependencies are installed: `pip install -r requirements.txt`
- Ensure Mistral AI API key is configured

**Chat not responding**
- Check internet connection
- Verify Mistral AI API key is valid
- Look for error messages in the chat panel

**File operations failing**
- Ensure workspace folder is open
- Check file permissions
- Verify file paths are correct

### Getting Help
- **Documentation**: Check the main project README
- **Issues**: Report bugs on GitHub
- **Community**: Join our Discord server
- **Support**: Email <EMAIL>

## Development

### Building from Source
```bash
# Clone repository
git clone <repository-url>
cd augment-agent-replica/vscode-extension

# Install dependencies
npm install

# Compile TypeScript
npm run compile

# Watch for changes
npm run watch

# Package extension
vsce package
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Changelog

### 1.0.0
- Initial release
- Complete chat panel interface
- All 23 tools implemented
- Context-aware suggestions
- Keyboard shortcuts and context menus
- Comprehensive configuration options

---

**Made with ❤️ by the Augment Code team**
