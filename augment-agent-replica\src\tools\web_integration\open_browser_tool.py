"""
Open Browser Tool - Exact replica of Augment Agent's open-browser functionality
Open URLs in the default browser with duplicate prevention
"""

import webbrowser
import time
from typing import Dict, Any, Set
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult

class OpenBrowserTool(BaseTool):
    """
    Open a URL in the default browser
    Exact replica of original Augment Agent open-browser tool
    """
    
    def __init__(self):
        super().__init__()
        # Track opened URLs to prevent duplicates
        self.opened_urls: Set[str] = set()
        self.last_opened_times: Dict[str, float] = {}
        self.duplicate_prevention_window = 300  # 5 minutes
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.WEB_INTEGRATION
    
    def _get_description(self) -> str:
        return """Open a URL in the default browser.

1. The tool takes in a URL and opens it in the default browser.
2. The tool does not return any content. It is intended for the user to visually inspect and interact with the page. You will not have access to it.
3. You should not use `open-browser` on a URL that you have called the tool on before in the conversation history, because the page is already open in the user's browser and the user can see it and refresh it themselves. Each time you call `open-browser`, it will jump the user to the browser window, which is highly annoying to the user."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "url": {
                    "description": "The URL to open in the browser.",
                    "type": "string"
                }
            },
            "required": ["url"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the open-browser tool"""
        try:
            url = kwargs.get('url', '')
            
            if not url:
                return ToolResult(
                    success=False,
                    error="url parameter is required"
                )
            
            # Validate URL format
            if not url.startswith(('http://', 'https://', 'file://', 'ftp://')):
                # Try to add https:// if it looks like a domain
                if '.' in url and not url.startswith('www.'):
                    url = 'https://' + url
                elif url.startswith('www.'):
                    url = 'https://' + url
                else:
                    return ToolResult(
                        success=False,
                        error="Invalid URL format. URL must start with http://, https://, file://, or ftp://"
                    )
            
            # Check for recent duplicate
            current_time = time.time()
            if url in self.last_opened_times:
                time_since_last = current_time - self.last_opened_times[url]
                if time_since_last < self.duplicate_prevention_window:
                    return ToolResult(
                        success=False,
                        error=f"URL was already opened {time_since_last:.0f} seconds ago. "
                               f"The page should still be open in the user's browser. "
                               f"Avoid reopening the same URL to prevent annoying the user."
                    )
            
            # Try to open the URL
            try:
                success = webbrowser.open(url)
                if not success:
                    # Try with different browsers if default fails
                    browsers = ['chrome', 'firefox', 'safari', 'edge']
                    for browser_name in browsers:
                        try:
                            browser = webbrowser.get(browser_name)
                            success = browser.open(url)
                            if success:
                                break
                        except webbrowser.Error:
                            continue
                
                if not success:
                    return ToolResult(
                        success=False,
                        error="Failed to open URL in any available browser"
                    )
            
            except Exception as e:
                return ToolResult(
                    success=False,
                    error=f"Failed to open browser: {str(e)}"
                )
            
            # Track the opened URL
            self.opened_urls.add(url)
            self.last_opened_times[url] = current_time
            
            # Clean up old entries to prevent memory growth
            self._cleanup_old_entries(current_time)
            
            return ToolResult(
                success=True,
                data={
                    'url': url,
                    'message': f"Successfully opened {url} in the default browser",
                    'timestamp': current_time
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error opening browser: {str(e)}"
            )
    
    def _cleanup_old_entries(self, current_time: float):
        """Clean up old URL tracking entries"""
        try:
            # Remove entries older than the prevention window
            urls_to_remove = []
            for url, timestamp in self.last_opened_times.items():
                if current_time - timestamp > self.duplicate_prevention_window:
                    urls_to_remove.append(url)
            
            for url in urls_to_remove:
                self.last_opened_times.pop(url, None)
                self.opened_urls.discard(url)
        
        except Exception:
            pass  # Ignore cleanup errors
