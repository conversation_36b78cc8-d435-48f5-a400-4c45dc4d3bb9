"""
Remove Files Tool - Exact replica of Augment Agent's remove-files functionality
Safe file deletion with backup and recovery options
"""

import os
import shutil
import time
from typing import Dict, Any, List
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult

class RemoveFilesTool(BaseTool):
    """
    Tool for safely removing files
    Exact replica of original Augment Agent remove-files tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.FILE_MANAGEMENT
    
    def _get_description(self) -> str:
        return """Remove files. ONLY use this tool to delete files in the user's workspace. This is the only safe tool to delete files in a way that the user can undo the change. Do NOT use the shell or launch-process tools to remove files."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "file_paths": {
                    "description": "The paths of the files to remove.",
                    "type": "array",
                    "items": {"type": "string"}
                }
            },
            "required": ["file_paths"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the remove-files tool"""
        try:
            file_paths = kwargs.get('file_paths', [])
            
            if not file_paths:
                return ToolResult(
                    success=False,
                    error="No file paths provided"
                )
            
            # Get workspace root from context
            workspace_root = kwargs.get('_context', {}).get('workspace_root', os.getcwd())
            
            # Create backup directory
            backup_dir = os.path.join(workspace_root, '.augment_backups', f"removal_{int(time.time())}")
            os.makedirs(backup_dir, exist_ok=True)
            
            removed_files = []
            failed_removals = []
            backed_up_files = []
            
            for relative_path in file_paths:
                try:
                    full_path = os.path.join(workspace_root, relative_path)
                    
                    # Check if file exists
                    if not os.path.exists(full_path):
                        failed_removals.append({
                            'path': relative_path,
                            'error': 'File not found'
                        })
                        continue
                    
                    # Check if it's actually a file (not directory)
                    if not os.path.isfile(full_path):
                        failed_removals.append({
                            'path': relative_path,
                            'error': 'Path is not a file'
                        })
                        continue
                    
                    # Create backup
                    backup_path = os.path.join(backup_dir, relative_path.replace('/', '_').replace('\\', '_'))
                    shutil.copy2(full_path, backup_path)
                    backed_up_files.append({
                        'original_path': relative_path,
                        'backup_path': backup_path
                    })
                    
                    # Get file info before removal
                    file_size = os.path.getsize(full_path)
                    file_mtime = os.path.getmtime(full_path)
                    
                    # Remove the file
                    os.remove(full_path)
                    
                    removed_files.append({
                        'path': relative_path,
                        'size_bytes': file_size,
                        'last_modified': file_mtime,
                        'backup_location': backup_path
                    })
                    
                except Exception as e:
                    failed_removals.append({
                        'path': relative_path,
                        'error': str(e)
                    })
            
            # Determine overall success
            success = len(removed_files) > 0 and len(failed_removals) == 0
            
            result_data = {
                'operation': 'remove_files',
                'files_removed': len(removed_files),
                'files_failed': len(failed_removals),
                'backup_directory': backup_dir,
                'removed_files': removed_files,
                'failed_removals': failed_removals,
                'recovery_instructions': f"To recover deleted files, copy them back from: {backup_dir}"
            }
            
            if failed_removals:
                error_msg = f"Failed to remove {len(failed_removals)} files: " + \
                           ", ".join([f"{f['path']} ({f['error']})" for f in failed_removals])
                
                if removed_files:
                    # Partial success
                    result_data['warning'] = error_msg
                    return ToolResult(
                        success=True,
                        data=result_data
                    )
                else:
                    # Complete failure
                    return ToolResult(
                        success=False,
                        error=error_msg,
                        data=result_data
                    )
            
            return ToolResult(
                success=True,
                data=result_data
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error in remove-files tool: {str(e)}"
            )
