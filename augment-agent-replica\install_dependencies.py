#!/usr/bin/env python3
"""
Dependency installer for Augment Agent Replica
Handles installation of all required packages with fallbacks
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description=""):
    """Run a command and handle errors gracefully"""
    print(f"🔧 {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Failed: {e}")
        if e.stdout:
            print(f"   stdout: {e.stdout}")
        if e.stderr:
            print(f"   stderr: {e.stderr}")
        return False

def install_core_dependencies():
    """Install core dependencies that are essential"""
    core_deps = [
        "fastapi>=0.104.1",
        "uvicorn>=0.24.0", 
        "pydantic>=2.5.0",
        "requests>=2.31.0",
        "beautifulsoup4>=4.12.0",
        "python-dotenv>=1.0.0",
        "pyyaml>=6.0.1",
        "psutil>=5.9.0",
        "aiofiles>=23.2.0"
    ]
    
    print("📦 Installing core dependencies...")
    for dep in core_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            print(f"⚠️  Failed to install {dep}, continuing...")
    
    return True

def install_mistral_ai():
    """Install Mistral AI with fallback versions"""
    print("🤖 Installing Mistral AI...")
    
    # Try different versions of Mistral AI
    versions = ["mistralai>=1.0.0", "mistralai>=0.4.0", "mistralai>=0.1.0"]
    
    for version in versions:
        if run_command(f"pip install {version}", f"Installing {version}"):
            return True
    
    print("⚠️  Could not install Mistral AI - agent will run in mock mode")
    return False

def install_optional_dependencies():
    """Install optional dependencies with graceful fallbacks"""
    optional_deps = {
        "html2text": "Web content processing",
        "selenium": "Browser automation", 
        "pytest": "Testing framework",
        "black": "Code formatting",
        "flake8": "Code linting"
    }
    
    print("📦 Installing optional dependencies...")
    for dep, description in optional_deps.items():
        run_command(f"pip install {dep}", f"Installing {dep} ({description})")

def create_env_file():
    """Create .env file if it doesn't exist"""
    env_file = Path(".env")
    if not env_file.exists():
        print("📝 Creating .env file...")
        env_content = """# Augment Agent Replica Configuration

# Mistral AI Configuration
MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_MODEL=mistral-large-latest
MISTRAL_MAX_TOKENS=4096
MISTRAL_TEMPERATURE=0.1

# Server Configuration
HOST=localhost
PORT=8000
LOG_LEVEL=INFO

# Workspace Configuration
WORKSPACE_ROOT=./workspace

# Optional: Google Search (for web-search tool)
# GOOGLE_SEARCH_API_KEY=your_google_api_key
# GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id

# Optional: Database URL (defaults to SQLite)
# DATABASE_URL=sqlite:///./data/agent.db

# Optional: Redis URL (for caching)
# REDIS_URL=redis://localhost:6379/0
"""
        with open(env_file, 'w') as f:
            f.write(env_content)
        print("✅ Created .env file")
    else:
        print("ℹ️  .env file already exists")

def create_directories():
    """Create necessary directories"""
    directories = [
        "logs",
        "data", 
        "workspace",
        "temp",
        "backups",
        "uploads",
        "indexes/codebase"
    ]
    
    print("📁 Creating directories...")
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    print("✅ Directories created")

def main():
    """Main installation function"""
    print("🤖 Augment Agent Replica - Dependency Installer")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Upgrade pip
    run_command("pip install --upgrade pip", "Upgrading pip")
    
    # Install dependencies
    install_core_dependencies()
    install_mistral_ai()
    install_optional_dependencies()
    
    # Setup environment
    create_env_file()
    create_directories()
    
    print("\n🎉 Installation completed!")
    print("\n📋 Next steps:")
    print("1. Edit .env file and add your Mistral AI API key")
    print("2. Run the agent:")
    print("   - Interactive mode: python src/main.py --interactive")
    print("   - Web interface: python src/main.py --server")
    print("   - Demo: python demo.py")
    print("\n📚 For more information, see README.md")

if __name__ == "__main__":
    main()
