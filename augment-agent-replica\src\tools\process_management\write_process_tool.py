"""
Write Process Tool - Exact replica of Augment Agent's write-process functionality
Send input to a running process
"""

from typing import Dict, Any
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult
from .process_manager import process_manager

class WriteProcessTool(BaseTool):
    """
    Write input to a terminal
    Exact replica of original Augment Agent write-process tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.PROCESS_MANAGEMENT
    
    def _get_description(self) -> str:
        return """Write input to a terminal."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "terminal_id": {
                    "description": "Terminal ID to write to.",
                    "type": "integer"
                },
                "input_text": {
                    "description": "Text to write to the process's stdin.",
                    "type": "string"
                }
            },
            "required": ["terminal_id", "input_text"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the write-process tool"""
        try:
            terminal_id = kwargs.get('terminal_id')
            input_text = kwargs.get('input_text', '')
            
            # Validate parameters
            if terminal_id is None:
                return ToolResult(
                    success=False,
                    error="terminal_id parameter is required"
                )
            
            if not isinstance(terminal_id, int):
                return ToolResult(
                    success=False,
                    error="terminal_id must be an integer"
                )
            
            if input_text is None:
                return ToolResult(
                    success=False,
                    error="input_text parameter is required"
                )
            
            # Write to process
            result = process_manager.write_process(
                terminal_id=terminal_id,
                input_text=input_text
            )
            
            if not result['success']:
                return ToolResult(
                    success=False,
                    error=result['error']
                )
            
            return ToolResult(
                success=True,
                data={
                    'terminal_id': terminal_id,
                    'input_sent': result['input_sent'],
                    'message': f"Input sent to terminal {terminal_id}"
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error writing to process: {str(e)}"
            )
