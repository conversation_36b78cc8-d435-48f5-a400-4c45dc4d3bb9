"""
List Processes Tool - Exact replica of Augment Agent's list-processes functionality
View all known terminals and their states
"""

from typing import Dict, Any
from ...core.tool_executor import <PERSON><PERSON>ool, Tool<PERSON>ategory, ToolResult
from .process_manager import process_manager

class ListProcessesTool(BaseTool):
    """
    List all known terminals created with the launch-process tool and their states
    Exact replica of original Augment Agent list-processes tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.PROCESS_MANAGEMENT
    
    def _get_description(self) -> str:
        return """List all known terminals created with the launch-process tool and their states."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {},
            "required": []
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the list-processes tool"""
        try:
            # Get all processes
            processes = process_manager.list_processes()
            
            # Format the process list
            formatted_processes = []
            for process in processes:
                formatted_process = {
                    'terminal_id': process['terminal_id'],
                    'command': process['command'],
                    'cwd': process['cwd'],
                    'state': process['state'],
                    'pid': process['pid'],
                    'return_code': process['return_code'],
                    'wait_mode': process['wait_mode']
                }
                
                # Add timing information
                if process['start_time']:
                    formatted_process['start_time'] = process['start_time']
                
                if process['end_time']:
                    formatted_process['end_time'] = process['end_time']
                    formatted_process['execution_time'] = process['end_time'] - process['start_time']
                
                formatted_processes.append(formatted_process)
            
            # Sort by terminal_id
            formatted_processes.sort(key=lambda x: x['terminal_id'])
            
            # Create summary
            summary = {
                'total_processes': len(formatted_processes),
                'running': len([p for p in formatted_processes if p['state'] == 'running']),
                'completed': len([p for p in formatted_processes if p['state'] == 'completed']),
                'failed': len([p for p in formatted_processes if p['state'] == 'failed']),
                'killed': len([p for p in formatted_processes if p['state'] == 'killed']),
                'timeout': len([p for p in formatted_processes if p['state'] == 'timeout'])
            }
            
            return ToolResult(
                success=True,
                data={
                    'summary': summary,
                    'processes': formatted_processes
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error listing processes: {str(e)}"
            )
