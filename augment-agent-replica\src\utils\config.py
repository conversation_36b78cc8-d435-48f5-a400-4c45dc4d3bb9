"""
Configuration management for Augment Agent Replica
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class Config:
    """Configuration class for Augment Agent Replica"""
    
    # Mistral AI Configuration
    mistral_api_key: str = "vlVy39wyXd1jkURNevvMkGuqKaPBj3Ek"
    mistral_model: str = "mistral-large-latest"
    mistral_max_tokens: int = 4096
    mistral_temperature: float = 0.1
    
    # Server Configuration
    host: str = "localhost"
    port: int = 8000
    debug: bool = True
    cors_origins: list = None
    
    # Security
    secret_key: str = ""
    jwt_secret: str = ""
    encryption_key: str = ""
    
    # File Management
    max_file_size: str = "100MB"
    workspace_root: str = "./workspace"
    temp_dir: str = "./temp"
    backup_dir: str = "./backups"
    
    # Process Management
    max_processes: int = 10
    process_timeout: int = 300
    terminal_history_size: int = 10000
    
    # Code Intelligence
    codebase_index_path: str = "./indexes/codebase"
    embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2"
    max_search_results: int = 50
    
    # Task Management
    task_db_path: str = "./data/tasks.db"
    max_tasks_per_project: int = 1000
    task_retention_days: int = 90
    
    # Memory System
    memory_db_path: str = "./data/memory.db"
    memory_retention_days: int = 365
    max_memories: int = 10000
    
    # Logging
    log_level: str = "INFO"
    log_file: str = "./logs/agent.log"
    log_max_size: str = "100MB"
    log_backup_count: int = 5
    
    # Performance
    cache_ttl: int = 3600
    max_concurrent_requests: int = 100
    request_timeout: int = 30
    
    # Web Interface
    static_files_path: str = "./src/web/static"
    templates_path: str = "./src/web/templates"
    upload_path: str = "./uploads"
    
    # External Services
    google_search_api_key: str = ""
    google_search_engine_id: str = ""
    github_token: str = ""
    gitlab_token: str = ""
    
    # Browser Automation
    selenium_driver_path: str = "./drivers/chromedriver"
    headless_browser: bool = True
    browser_timeout: int = 30
    
    def __post_init__(self):
        """Initialize configuration from environment variables"""
        self._load_from_env()
        self._validate_config()
        self._create_directories()
    
    def _load_from_env(self):
        """Load configuration from environment variables"""
        env_mappings = {
            'mistral_api_key': 'MISTRAL_API_KEY',
            'mistral_model': 'MISTRAL_MODEL',
            'mistral_max_tokens': ('MISTRAL_MAX_TOKENS', int),
            'mistral_temperature': ('MISTRAL_TEMPERATURE', float),
            'host': 'HOST',
            'port': ('PORT', int),
            'debug': ('DEBUG', lambda x: x.lower() == 'true'),
            'secret_key': 'SECRET_KEY',
            'jwt_secret': 'JWT_SECRET',
            'workspace_root': 'WORKSPACE_ROOT',
            'temp_dir': 'TEMP_DIR',
            'backup_dir': 'BACKUP_DIR',
            'max_processes': ('MAX_PROCESSES', int),
            'process_timeout': ('PROCESS_TIMEOUT', int),
            'log_level': 'LOG_LEVEL',
            'log_file': 'LOG_FILE',
            'google_search_api_key': 'GOOGLE_SEARCH_API_KEY',
            'google_search_engine_id': 'GOOGLE_SEARCH_ENGINE_ID',
            'github_token': 'GITHUB_TOKEN'
        }
        
        for attr, env_config in env_mappings.items():
            if isinstance(env_config, tuple):
                env_var, converter = env_config
            else:
                env_var, converter = env_config, str
            
            env_value = os.getenv(env_var)
            if env_value is not None:
                try:
                    setattr(self, attr, converter(env_value))
                except (ValueError, TypeError) as e:
                    print(f"Warning: Invalid value for {env_var}: {env_value} ({e})")
        
        # Handle CORS origins specially
        cors_origins_env = os.getenv('CORS_ORIGINS')
        if cors_origins_env:
            self.cors_origins = [origin.strip() for origin in cors_origins_env.split(',')]
        else:
            self.cors_origins = ["http://localhost:3000", "http://localhost:8000"]
    
    def _validate_config(self):
        """Validate configuration values"""
        if not self.mistral_api_key or self.mistral_api_key == "vlVy39wyXd1jkURNevvMkGuqKaPBj3Ek":
            print("Warning: MISTRAL_API_KEY not configured - using mock client")
        
        if not self.secret_key:
            self.secret_key = os.urandom(32).hex()
            print("Warning: Generated random SECRET_KEY. Set SECRET_KEY environment variable for production.")
        
        if not self.jwt_secret:
            self.jwt_secret = os.urandom(32).hex()
            print("Warning: Generated random JWT_SECRET. Set JWT_SECRET environment variable for production.")
        
        # Validate paths
        for path_attr in ['workspace_root', 'temp_dir', 'backup_dir']:
            path_value = getattr(self, path_attr)
            if not os.path.isabs(path_value):
                setattr(self, path_attr, os.path.abspath(path_value))
    
    def _create_directories(self):
        """Create necessary directories"""
        directories = [
            self.workspace_root,
            self.temp_dir,
            self.backup_dir,
            os.path.dirname(self.log_file),
            os.path.dirname(self.task_db_path),
            os.path.dirname(self.memory_db_path),
            self.codebase_index_path,
            self.upload_path
        ]
        
        for directory in directories:
            if directory:
                os.makedirs(directory, exist_ok=True)
    
    def get_database_url(self) -> str:
        """Get database URL"""
        return f"sqlite:///{self.task_db_path}"
    
    def get_redis_url(self) -> str:
        """Get Redis URL"""
        return os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary"""
        return {
            key: value for key, value in self.__dict__.items()
            if not key.startswith('_')
        }
    
    @classmethod
    def from_file(cls, config_file: str) -> 'Config':
        """Load configuration from file"""
        import json
        import yaml
        
        config_path = Path(config_file)
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_file}")
        
        if config_path.suffix.lower() == '.json':
            with open(config_path, 'r') as f:
                config_data = json.load(f)
        elif config_path.suffix.lower() in ['.yml', '.yaml']:
            with open(config_path, 'r') as f:
                config_data = yaml.safe_load(f)
        else:
            raise ValueError(f"Unsupported configuration file format: {config_path.suffix}")
        
        # Create config instance and update with file data
        config = cls()
        for key, value in config_data.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        return config
    
    def save_to_file(self, config_file: str):
        """Save configuration to file"""
        import json
        import yaml
        
        config_path = Path(config_file)
        config_data = self.to_dict()
        
        # Remove sensitive data
        sensitive_keys = ['mistral_api_key', 'secret_key', 'jwt_secret', 'encryption_key']
        for key in sensitive_keys:
            if key in config_data:
                config_data[key] = "***REDACTED***"
        
        if config_path.suffix.lower() == '.json':
            with open(config_path, 'w') as f:
                json.dump(config_data, f, indent=2)
        elif config_path.suffix.lower() in ['.yml', '.yaml']:
            with open(config_path, 'w') as f:
                yaml.dump(config_data, f, default_flow_style=False)
        else:
            raise ValueError(f"Unsupported configuration file format: {config_path.suffix}")

# Global configuration instance
config = Config()
