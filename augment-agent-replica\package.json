{"name": "augment-agent-replica", "version": "1.0.0", "description": "Complete replica of Augment Agent with all tools and capabilities", "main": "src/web/app.js", "scripts": {"start": "node src/web/app.js", "dev": "nodemon src/web/app.js", "build": "webpack --mode production", "build:dev": "webpack --mode development", "test": "jest", "lint": "eslint src/", "format": "prettier --write src/"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "axios": "^1.6.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "validator": "^13.11.0", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.1", "ws": "^8.14.2", "node-cron": "^3.0.3", "chokidar": "^3.5.3", "mime-types": "^2.1.35", "marked": "^9.1.6", "highlight.js": "^11.9.0", "mermaid": "^10.6.1", "monaco-editor": "^0.44.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0", "codemirror": "^6.0.1", "@codemirror/lang-javascript": "^6.2.1", "@codemirror/lang-python": "^6.1.3", "@codemirror/lang-html": "^6.4.7", "@codemirror/lang-css": "^6.2.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/theme-one-dark": "^6.1.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "styled-components": "^6.1.1", "framer-motion": "^10.16.5", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-syntax-highlighter": "^15.5.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "rehype-highlight": "^7.0.0"}, "devDependencies": {"webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "babel-loader": "^9.1.3", "@babel/core": "^7.23.3", "@babel/preset-env": "^7.23.3", "@babel/preset-react": "^7.23.3", "css-loader": "^6.8.1", "style-loader": "^3.3.3", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.3", "mini-css-extract-plugin": "^2.7.6", "nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0", "@types/node": "^20.9.0", "typescript": "^5.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/augment-agent-replica.git"}, "keywords": ["ai", "agent", "mistral", "coding-assistant", "automation", "development-tools"], "author": "Your Name", "license": "MIT", "bugs": {"url": "https://github.com/your-username/augment-agent-replica/issues"}, "homepage": "https://github.com/your-username/augment-agent-replica#readme"}