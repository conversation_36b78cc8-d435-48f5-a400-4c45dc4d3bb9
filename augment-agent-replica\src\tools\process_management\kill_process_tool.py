"""
Kill Process Tool - Exact replica of Augment Agent's kill-process functionality
Terminate processes by terminal ID
"""

from typing import Dict, Any
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult
from .process_manager import process_manager

class KillProcessTool(BaseTool):
    """
    Kill a process by its terminal ID
    Exact replica of original Augment Agent kill-process tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.PROCESS_MANAGEMENT
    
    def _get_description(self) -> str:
        return """Kill a process by its terminal ID."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "terminal_id": {
                    "description": "Terminal ID to kill.",
                    "type": "integer"
                }
            },
            "required": ["terminal_id"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the kill-process tool"""
        try:
            terminal_id = kwargs.get('terminal_id')
            
            # Validate parameters
            if terminal_id is None:
                return ToolResult(
                    success=False,
                    error="terminal_id parameter is required"
                )
            
            if not isinstance(terminal_id, int):
                return ToolResult(
                    success=False,
                    error="terminal_id must be an integer"
                )
            
            # Kill the process
            result = process_manager.kill_process(terminal_id)
            
            if not result['success']:
                return ToolResult(
                    success=False,
                    error=result['error']
                )
            
            return ToolResult(
                success=True,
                data={
                    'terminal_id': terminal_id,
                    'message': result['message']
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error killing process: {str(e)}"
            )
