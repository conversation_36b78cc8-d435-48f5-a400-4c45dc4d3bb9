#!/bin/bash

# Installation script for Augment Agent Replica VSCode Extension

echo "🤖 Installing Augment Agent Replica VSCode Extension..."

# Check if VSCode is installed
if ! command -v code &> /dev/null; then
    echo "❌ VSCode is not installed or not in PATH."
    echo "Please install VSCode first: https://code.visualstudio.com/"
    exit 1
fi

# Check if the extension package exists
EXTENSION_FILE="augment-agent-replica-1.0.0.vsix"

if [ ! -f "$EXTENSION_FILE" ]; then
    echo "📦 Extension package not found. Building extension..."
    ./build.sh
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed to build extension"
        exit 1
    fi
fi

# Install the extension
echo "🔧 Installing extension..."
code --install-extension "$EXTENSION_FILE"

if [ $? -ne 0 ]; then
    echo "❌ Failed to install extension"
    exit 1
fi

echo "✅ Extension installed successfully!"
echo ""
echo "🚀 Next steps:"
echo "1. Restart VSCode"
echo "2. Open a workspace/folder"
echo "3. Press Ctrl+Shift+A to open the chat panel"
echo "4. Configure your Mistral AI API key in settings"
echo ""
echo "⚙️ Configuration:"
echo "- Go to File > Preferences > Settings"
echo "- Search for 'Augment Agent'"
echo "- Enter your Mistral AI API key"
echo ""
echo "📚 For more help, see the README.md file"
