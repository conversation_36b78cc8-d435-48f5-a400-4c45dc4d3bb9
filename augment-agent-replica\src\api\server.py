"""
FastAPI server for Augment Agent Replica
Provides REST API and WebSocket interfaces
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, WebSocket, WebSocketDisconnect, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel
import uvicorn

try:
    from ..core.agent import AugmentAgentReplica
    from ..utils.config import Config
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from core.agent import AugmentAgentReplica
    from utils.config import Config

logger = logging.getLogger(__name__)

# Pydantic models for API
class ChatRequest(BaseModel):
    message: str
    stream: bool = False
    context: Optional[Dict[str, Any]] = None

class ChatResponse(BaseModel):
    response: str
    success: bool = True
    error: Optional[str] = None

class ToolExecuteRequest(BaseModel):
    tool: str
    parameters: Dict[str, Any]

class ToolExecuteResponse(BaseModel):
    success: bool
    data: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    tool_name: str = ""

class AgentStatusResponse(BaseModel):
    status: str
    tools_registered: int
    conversation_length: int
    execution_stats: Dict[str, Any]
    context: Dict[str, Any]
    behavior_patterns: Dict[str, bool]
    model_info: Dict[str, Any]

def create_app(agent: AugmentAgentReplica, config: Config) -> FastAPI:
    """Create FastAPI application"""
    
    app = FastAPI(
        title="Augment Agent Replica API",
        description="Complete replica of Augment Agent with all tools and capabilities",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=config.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # WebSocket connection manager
    class ConnectionManager:
        def __init__(self):
            self.active_connections: List[WebSocket] = []
        
        async def connect(self, websocket: WebSocket):
            await websocket.accept()
            self.active_connections.append(websocket)
        
        def disconnect(self, websocket: WebSocket):
            self.active_connections.remove(websocket)
        
        async def send_personal_message(self, message: str, websocket: WebSocket):
            await websocket.send_text(message)
        
        async def broadcast(self, message: str):
            for connection in self.active_connections:
                await connection.send_text(message)
    
    manager = ConnectionManager()
    
    # Routes
    @app.get("/", response_class=HTMLResponse)
    async def root():
        """Serve the main web interface"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Augment Agent Replica</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .header { text-align: center; margin-bottom: 30px; }
                .chat-container { border: 1px solid #ddd; border-radius: 8px; height: 400px; overflow-y: auto; padding: 15px; margin-bottom: 20px; background: #fafafa; }
                .input-container { display: flex; gap: 10px; }
                .message-input { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
                .send-button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
                .send-button:hover { background: #0056b3; }
                .message { margin-bottom: 15px; }
                .user-message { text-align: right; }
                .agent-message { text-align: left; }
                .message-content { display: inline-block; padding: 8px 12px; border-radius: 8px; max-width: 70%; }
                .user-message .message-content { background: #007bff; color: white; }
                .agent-message .message-content { background: #e9ecef; color: #333; }
                .tool-result { background: #d4edda; border: 1px solid #c3e6cb; padding: 8px; border-radius: 4px; margin: 5px 0; font-size: 0.9em; }
                .tool-error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 8px; border-radius: 4px; margin: 5px 0; font-size: 0.9em; }
                .status-bar { background: #e9ecef; padding: 10px; border-radius: 4px; margin-bottom: 20px; font-size: 0.9em; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🤖 Augment Agent Replica</h1>
                    <p>AI Coding Assistant with 25+ Tools</p>
                </div>
                
                <div class="status-bar" id="status">
                    Status: Connecting...
                </div>
                
                <div class="chat-container" id="chat">
                    <div class="message agent-message">
                        <div class="message-content">
                            Hello! I'm Augment Agent Replica. I have access to 25+ tools for file management, code intelligence, process management, web integration, task management, and more. How can I help you today?
                        </div>
                    </div>
                </div>
                
                <div class="input-container">
                    <input type="text" class="message-input" id="messageInput" placeholder="Type your message here..." onkeypress="handleKeyPress(event)">
                    <button class="send-button" onclick="sendMessage()">Send</button>
                </div>
            </div>
            
            <script>
                const ws = new WebSocket(`ws://${window.location.host}/ws`);
                const chat = document.getElementById('chat');
                const messageInput = document.getElementById('messageInput');
                const status = document.getElementById('status');
                
                ws.onopen = function(event) {
                    status.textContent = 'Status: Connected ✅';
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleMessage(data);
                };
                
                ws.onclose = function(event) {
                    status.textContent = 'Status: Disconnected ❌';
                };
                
                function handleMessage(data) {
                    if (data.type === 'content') {
                        appendAgentMessage(data.content);
                    } else if (data.type === 'tool_result') {
                        appendToolResult(data);
                    } else if (data.type === 'error') {
                        appendError(data.content);
                    }
                }
                
                function appendUserMessage(message) {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'message user-message';
                    messageDiv.innerHTML = `<div class="message-content">${escapeHtml(message)}</div>`;
                    chat.appendChild(messageDiv);
                    chat.scrollTop = chat.scrollHeight;
                }
                
                function appendAgentMessage(content) {
                    let lastMessage = chat.lastElementChild;
                    if (lastMessage && lastMessage.classList.contains('agent-message')) {
                        lastMessage.querySelector('.message-content').innerHTML += escapeHtml(content);
                    } else {
                        const messageDiv = document.createElement('div');
                        messageDiv.className = 'message agent-message';
                        messageDiv.innerHTML = `<div class="message-content">${escapeHtml(content)}</div>`;
                        chat.appendChild(messageDiv);
                    }
                    chat.scrollTop = chat.scrollHeight;
                }
                
                function appendToolResult(data) {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = data.success ? 'tool-result' : 'tool-error';
                    const icon = data.success ? '✅' : '❌';
                    resultDiv.innerHTML = `${icon} Tool: ${data.tool_name} (${data.execution_time.toFixed(2)}s)`;
                    if (!data.success) {
                        resultDiv.innerHTML += `<br>Error: ${escapeHtml(data.error)}`;
                    }
                    chat.appendChild(resultDiv);
                    chat.scrollTop = chat.scrollHeight;
                }
                
                function appendError(error) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'tool-error';
                    errorDiv.innerHTML = `❌ Error: ${escapeHtml(error)}`;
                    chat.appendChild(errorDiv);
                    chat.scrollTop = chat.scrollHeight;
                }
                
                function sendMessage() {
                    const message = messageInput.value.trim();
                    if (message) {
                        appendUserMessage(message);
                        ws.send(JSON.stringify({message: message, stream: true}));
                        messageInput.value = '';
                    }
                }
                
                function handleKeyPress(event) {
                    if (event.key === 'Enter') {
                        sendMessage();
                    }
                }
                
                function escapeHtml(text) {
                    const div = document.createElement('div');
                    div.textContent = text;
                    return div.innerHTML;
                }
            </script>
        </body>
        </html>
        """
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {"status": "healthy", "service": "augment-agent-replica"}
    
    @app.get("/status", response_model=AgentStatusResponse)
    async def get_status():
        """Get agent status"""
        status = agent.get_agent_status()
        return AgentStatusResponse(**status)
    
    @app.get("/tools")
    async def get_tools():
        """Get available tools"""
        return {"tools": agent.get_available_tools()}
    
    @app.post("/api/chat", response_model=ChatResponse)
    async def chat_endpoint(request: ChatRequest):
        """Chat with the agent (non-streaming)"""
        try:
            response_parts = []
            async for chunk in agent.chat(request.message, stream=False, context=request.context):
                if chunk['type'] == 'content':
                    response_parts.append(chunk['content'])
            
            return ChatResponse(response=''.join(response_parts))
            
        except Exception as e:
            logger.error(f"Error in chat endpoint: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/tools/execute", response_model=ToolExecuteResponse)
    async def execute_tool_endpoint(request: ToolExecuteRequest):
        """Execute a tool directly"""
        try:
            result = await agent.execute_tool_directly(request.tool, request.parameters)
            return ToolExecuteResponse(**result)
            
        except Exception as e:
            logger.error(f"Error executing tool: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.websocket("/ws")
    async def websocket_endpoint(websocket: WebSocket):
        """WebSocket endpoint for real-time chat"""
        await manager.connect(websocket)
        try:
            while True:
                # Receive message from client
                data = await websocket.receive_text()
                request_data = json.loads(data)
                
                message = request_data.get('message', '')
                stream = request_data.get('stream', True)
                context = request_data.get('context')
                
                # Stream response back to client
                async for chunk in agent.chat(message, stream=stream, context=context):
                    await manager.send_personal_message(
                        json.dumps(chunk), 
                        websocket
                    )
                    
        except WebSocketDisconnect:
            manager.disconnect(websocket)
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
            await manager.send_personal_message(
                json.dumps({"type": "error", "content": str(e)}),
                websocket
            )
    
    return app
