"""
Read Process Tool - Exact replica of Augment Agent's read-process functionality
Read output from a terminal with wait options
"""

from typing import Dict, Any
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult
from .process_manager import process_manager

class ReadProcessTool(BaseTool):
    """
    Read output from a terminal
    Exact replica of original Augment Agent read-process tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.PROCESS_MANAGEMENT
    
    def _get_description(self) -> str:
        return """Read output from a terminal.

If `wait=true` and the process has not yet completed, waits for the terminal to complete up to `max_wait_seconds` seconds before returning its output.

If `wait=false` or the process has already completed, returns immediately with the current output."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "terminal_id": {
                    "description": "Terminal ID to read from.",
                    "type": "integer"
                },
                "wait": {
                    "description": "Whether to wait for the command to complete.",
                    "type": "boolean"
                },
                "max_wait_seconds": {
                    "description": "Number of seconds to wait for the command to complete. Only relevant when wait=true. 1 minute may be a good default: increase from there if needed.",
                    "type": "number"
                }
            },
            "required": ["terminal_id", "wait", "max_wait_seconds"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the read-process tool"""
        try:
            terminal_id = kwargs.get('terminal_id')
            wait = kwargs.get('wait', False)
            max_wait_seconds = kwargs.get('max_wait_seconds', 60)
            
            # Validate parameters
            if terminal_id is None:
                return ToolResult(
                    success=False,
                    error="terminal_id parameter is required"
                )
            
            if not isinstance(terminal_id, int):
                return ToolResult(
                    success=False,
                    error="terminal_id must be an integer"
                )
            
            # Read from process
            result = process_manager.read_process(
                terminal_id=terminal_id,
                wait=wait,
                max_wait_seconds=max_wait_seconds
            )
            
            if not result['success']:
                return ToolResult(
                    success=False,
                    error=result['error']
                )
            
            # Format the output
            output_data = {
                'terminal_id': terminal_id,
                'state': result['state'],
                'return_code': result['return_code'],
                'stdout': result['stdout'],
                'stderr': result['stderr'],
                'pid': result['pid'],
                'command': result['command'],
                'cwd': result['cwd'],
                'start_time': result['start_time'],
                'end_time': result['end_time']
            }
            
            # Add execution time if process has ended
            if result['end_time']:
                output_data['execution_time'] = result['end_time'] - result['start_time']
            
            # Add combined output for convenience
            combined_output = result['stdout']
            if result['stderr']:
                if combined_output:
                    combined_output += '\n--- STDERR ---\n'
                combined_output += result['stderr']
            
            output_data['combined_output'] = combined_output
            
            return ToolResult(
                success=True,
                data=output_data
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error reading process: {str(e)}"
            )
