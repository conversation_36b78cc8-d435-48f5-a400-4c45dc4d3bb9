"""
Render Mermaid Tool - Exact replica of Augment Agent's render-mermaid functionality
Interactive diagram creation with pan/zoom controls and copy functionality
"""

import base64
import json
import tempfile
import os
from typing import Dict, Any, List
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult

class RenderMermaidTool(BaseTool):
    """
    Render a Mermaid diagram from the provided definition
    Exact replica of original Augment Agent render-mermaid tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.MEMORY
    
    def _get_description(self) -> str:
        return """Render a Mermaid diagram from the provided definition. This tool takes Mermaid diagram code and renders it as an interactive diagram with pan/zoom controls and copy functionality."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "diagram_definition": {
                    "description": "The Mermaid diagram definition code to render",
                    "type": "string"
                },
                "title": {
                    "description": "Optional title for the diagram",
                    "type": "string",
                    "default": "Mermaid Diagram"
                }
            },
            "required": ["diagram_definition"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the render-mermaid tool"""
        try:
            diagram_definition = kwargs.get('diagram_definition', '')
            title = kwargs.get('title', 'Mermaid Diagram')
            
            if not diagram_definition:
                return ToolResult(
                    success=False,
                    error="diagram_definition parameter is required"
                )
            
            # Validate Mermaid syntax
            validation_result = self._validate_mermaid_syntax(diagram_definition)
            if not validation_result['valid']:
                return ToolResult(
                    success=False,
                    error=f"Invalid Mermaid syntax: {validation_result['error']}"
                )
            
            # Generate HTML with embedded Mermaid
            html_content = self._generate_mermaid_html(diagram_definition, title)
            
            # Save to temporary file
            temp_file = self._save_to_temp_file(html_content, title)
            
            # Generate diagram metadata
            metadata = self._analyze_diagram(diagram_definition)
            
            return ToolResult(
                success=True,
                data={
                    'title': title,
                    'diagram_definition': diagram_definition,
                    'diagram_type': metadata['type'],
                    'node_count': metadata['node_count'],
                    'edge_count': metadata['edge_count'],
                    'html_file': temp_file,
                    'html_content': html_content,
                    'message': f"Successfully rendered {metadata['type']} diagram with {metadata['node_count']} nodes"
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error rendering Mermaid diagram: {str(e)}"
            )
    
    def _validate_mermaid_syntax(self, diagram_definition: str) -> Dict[str, Any]:
        """Validate Mermaid diagram syntax"""
        try:
            lines = diagram_definition.strip().split('\n')
            if not lines:
                return {'valid': False, 'error': 'Empty diagram definition'}
            
            first_line = lines[0].strip()
            
            # Check for valid diagram types
            valid_types = [
                'graph', 'flowchart', 'sequenceDiagram', 'classDiagram',
                'stateDiagram', 'erDiagram', 'journey', 'gantt',
                'pie', 'gitgraph', 'mindmap', 'timeline'
            ]
            
            diagram_type = None
            for vtype in valid_types:
                if first_line.startswith(vtype):
                    diagram_type = vtype
                    break
            
            if not diagram_type:
                return {
                    'valid': False, 
                    'error': f'Unknown diagram type. Must start with one of: {", ".join(valid_types)}'
                }
            
            # Basic syntax validation
            if diagram_type in ['graph', 'flowchart']:
                return self._validate_graph_syntax(lines)
            elif diagram_type == 'sequenceDiagram':
                return self._validate_sequence_syntax(lines)
            elif diagram_type == 'classDiagram':
                return self._validate_class_syntax(lines)
            else:
                # For other types, just check basic structure
                return {'valid': True, 'type': diagram_type}
        
        except Exception as e:
            return {'valid': False, 'error': f'Syntax validation error: {str(e)}'}
    
    def _validate_graph_syntax(self, lines: List[str]) -> Dict[str, Any]:
        """Validate graph/flowchart syntax"""
        try:
            # Check for basic graph syntax patterns
            for i, line in enumerate(lines[1:], 2):  # Skip first line
                line = line.strip()
                if not line or line.startswith('%'):  # Skip empty lines and comments
                    continue
                
                # Basic patterns for graph syntax
                valid_patterns = [
                    r'^\s*[A-Za-z0-9_]+\s*$',  # Node definition
                    r'^\s*[A-Za-z0-9_]+\s*\[.*\]\s*$',  # Node with label
                    r'^\s*[A-Za-z0-9_]+\s*-->.*$',  # Arrow connection
                    r'^\s*[A-Za-z0-9_]+\s*---.*$',  # Line connection
                    r'^\s*[A-Za-z0-9_]+\s*-\.-.*$',  # Dotted connection
                    r'^\s*[A-Za-z0-9_]+\s*==>.*$',  # Thick arrow
                    r'^\s*subgraph.*$',  # Subgraph start
                    r'^\s*end\s*$',  # Subgraph end
                    r'^\s*style.*$',  # Style definition
                    r'^\s*class.*$',  # Class definition
                    r'^\s*click.*$'   # Click event
                ]
                
                import re
                if not any(re.match(pattern, line) for pattern in valid_patterns):
                    # Allow some flexibility for complex syntax
                    if '-->' in line or '---' in line or '==>' in line:
                        continue
                    return {
                        'valid': False, 
                        'error': f'Invalid syntax at line {i}: {line}'
                    }
            
            return {'valid': True, 'type': 'graph'}
        
        except Exception as e:
            return {'valid': False, 'error': f'Graph validation error: {str(e)}'}
    
    def _validate_sequence_syntax(self, lines: List[str]) -> Dict[str, Any]:
        """Validate sequence diagram syntax"""
        try:
            for i, line in enumerate(lines[1:], 2):
                line = line.strip()
                if not line or line.startswith('%'):
                    continue
                
                # Basic sequence diagram patterns
                if ('->>' in line or '->' in line or 
                    line.startswith('participant') or 
                    line.startswith('actor') or
                    line.startswith('note') or
                    line.startswith('activate') or
                    line.startswith('deactivate') or
                    line.startswith('loop') or
                    line.startswith('end') or
                    line.startswith('alt') or
                    line.startswith('else') or
                    line.startswith('opt')):
                    continue
                
                return {
                    'valid': False,
                    'error': f'Invalid sequence diagram syntax at line {i}: {line}'
                }
            
            return {'valid': True, 'type': 'sequenceDiagram'}
        
        except Exception as e:
            return {'valid': False, 'error': f'Sequence validation error: {str(e)}'}
    
    def _validate_class_syntax(self, lines: List[str]) -> Dict[str, Any]:
        """Validate class diagram syntax"""
        try:
            for i, line in enumerate(lines[1:], 2):
                line = line.strip()
                if not line or line.startswith('%'):
                    continue
                
                # Basic class diagram patterns
                if (line.startswith('class ') or
                    '-->' in line or '--|>' in line or
                    '..|>' in line or '..' in line or
                    line.startswith('<<') or
                    ':' in line):
                    continue
                
                # Allow class definitions and relationships
                if any(char in line for char in [':', '{', '}', '(', ')', '+', '-', '#', '~']):
                    continue
                
                return {
                    'valid': False,
                    'error': f'Invalid class diagram syntax at line {i}: {line}'
                }
            
            return {'valid': True, 'type': 'classDiagram'}
        
        except Exception as e:
            return {'valid': False, 'error': f'Class validation error: {str(e)}'}
    
    def _generate_mermaid_html(self, diagram_definition: str, title: str) -> str:
        """Generate HTML with embedded Mermaid diagram"""
        
        # Escape the diagram definition for JavaScript
        escaped_definition = json.dumps(diagram_definition)
        
        html_template = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 100%;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .title {{
            font-size: 1.5em;
            font-weight: bold;
        }}
        .controls {{
            display: flex;
            gap: 10px;
        }}
        .btn {{
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }}
        .btn:hover {{
            background: #2980b9;
        }}
        .diagram-container {{
            padding: 20px;
            text-align: center;
            min-height: 400px;
            overflow: auto;
        }}
        #mermaid-diagram {{
            max-width: 100%;
            height: auto;
        }}
        .source-container {{
            border-top: 1px solid #eee;
            padding: 20px;
            background: #f8f9fa;
        }}
        .source-title {{
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }}
        .source-code {{
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            overflow-x: auto;
        }}
        .toast {{
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 12px 20px;
            border-radius: 4px;
            display: none;
            z-index: 1000;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">{title}</div>
            <div class="controls">
                <button class="btn" onclick="zoomIn()">Zoom In</button>
                <button class="btn" onclick="zoomOut()">Zoom Out</button>
                <button class="btn" onclick="resetZoom()">Reset</button>
                <button class="btn" onclick="copyDiagram()">Copy Code</button>
                <button class="btn" onclick="downloadSVG()">Download SVG</button>
            </div>
        </div>
        
        <div class="diagram-container">
            <div id="mermaid-diagram"></div>
        </div>
        
        <div class="source-container">
            <div class="source-title">Mermaid Source Code:</div>
            <div class="source-code" id="source-code">{diagram_definition}</div>
        </div>
    </div>
    
    <div class="toast" id="toast"></div>
    
    <script>
        // Initialize Mermaid
        mermaid.initialize({{
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'Arial, sans-serif'
        }});
        
        // Render the diagram
        const diagramDefinition = {escaped_definition};
        
        async function renderDiagram() {{
            try {{
                const element = document.getElementById('mermaid-diagram');
                const {{ svg }} = await mermaid.render('diagram', diagramDefinition);
                element.innerHTML = svg;
                
                // Add pan and zoom functionality
                addPanZoom();
            }} catch (error) {{
                console.error('Error rendering diagram:', error);
                document.getElementById('mermaid-diagram').innerHTML = 
                    '<div style="color: red; padding: 20px;">Error rendering diagram: ' + error.message + '</div>';
            }}
        }}
        
        let currentZoom = 1;
        let svgElement;
        
        function addPanZoom() {{
            svgElement = document.querySelector('#mermaid-diagram svg');
            if (svgElement) {{
                svgElement.style.cursor = 'grab';
                svgElement.style.transition = 'transform 0.2s ease';
                
                let isPanning = false;
                let startX, startY, translateX = 0, translateY = 0;
                
                svgElement.addEventListener('mousedown', (e) => {{
                    isPanning = true;
                    startX = e.clientX - translateX;
                    startY = e.clientY - translateY;
                    svgElement.style.cursor = 'grabbing';
                }});
                
                document.addEventListener('mousemove', (e) => {{
                    if (!isPanning) return;
                    translateX = e.clientX - startX;
                    translateY = e.clientY - startY;
                    updateTransform();
                }});
                
                document.addEventListener('mouseup', () => {{
                    isPanning = false;
                    if (svgElement) svgElement.style.cursor = 'grab';
                }});
                
                // Zoom with mouse wheel
                svgElement.addEventListener('wheel', (e) => {{
                    e.preventDefault();
                    const delta = e.deltaY > 0 ? 0.9 : 1.1;
                    currentZoom *= delta;
                    currentZoom = Math.max(0.1, Math.min(5, currentZoom));
                    updateTransform();
                }});
            }}
        }}
        
        function updateTransform() {{
            if (svgElement) {{
                svgElement.style.transform = `translate(${{translateX}}px, ${{translateY}}px) scale(${{currentZoom}})`;
            }}
        }}
        
        function zoomIn() {{
            currentZoom *= 1.2;
            currentZoom = Math.min(5, currentZoom);
            updateTransform();
        }}
        
        function zoomOut() {{
            currentZoom *= 0.8;
            currentZoom = Math.max(0.1, currentZoom);
            updateTransform();
        }}
        
        function resetZoom() {{
            currentZoom = 1;
            translateX = 0;
            translateY = 0;
            updateTransform();
        }}
        
        function copyDiagram() {{
            navigator.clipboard.writeText(diagramDefinition).then(() => {{
                showToast('Diagram code copied to clipboard!');
            }}).catch(() => {{
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = diagramDefinition;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('Diagram code copied to clipboard!');
            }});
        }}
        
        function downloadSVG() {{
            const svg = document.querySelector('#mermaid-diagram svg');
            if (svg) {{
                const svgData = new XMLSerializer().serializeToString(svg);
                const blob = new Blob([svgData], {{type: 'image/svg+xml'}});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = '{title.replace(" ", "_")}.svg';
                a.click();
                URL.revokeObjectURL(url);
                showToast('SVG downloaded!');
            }}
        }}
        
        function showToast(message) {{
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.style.display = 'block';
            setTimeout(() => {{
                toast.style.display = 'none';
            }}, 3000);
        }}
        
        // Render diagram when page loads
        renderDiagram();
    </script>
</body>
</html>'''
        
        return html_template
    
    def _save_to_temp_file(self, html_content: str, title: str) -> str:
        """Save HTML content to temporary file"""
        try:
            # Create temp file with .html extension
            temp_dir = tempfile.gettempdir()
            safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"mermaid_{safe_title.replace(' ', '_')}.html"
            temp_file = os.path.join(temp_dir, filename)
            
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return temp_file
        
        except Exception as e:
            # Return a generic temp file path if saving fails
            return os.path.join(tempfile.gettempdir(), "mermaid_diagram.html")
    
    def _analyze_diagram(self, diagram_definition: str) -> Dict[str, Any]:
        """Analyze diagram to extract metadata"""
        lines = diagram_definition.strip().split('\n')
        first_line = lines[0].strip()
        
        # Determine diagram type
        diagram_type = 'unknown'
        if first_line.startswith('graph'):
            diagram_type = 'flowchart'
        elif first_line.startswith('flowchart'):
            diagram_type = 'flowchart'
        elif first_line.startswith('sequenceDiagram'):
            diagram_type = 'sequence'
        elif first_line.startswith('classDiagram'):
            diagram_type = 'class'
        elif first_line.startswith('stateDiagram'):
            diagram_type = 'state'
        elif first_line.startswith('erDiagram'):
            diagram_type = 'entity-relationship'
        elif first_line.startswith('journey'):
            diagram_type = 'user-journey'
        elif first_line.startswith('gantt'):
            diagram_type = 'gantt'
        elif first_line.startswith('pie'):
            diagram_type = 'pie'
        elif first_line.startswith('gitgraph'):
            diagram_type = 'git'
        elif first_line.startswith('mindmap'):
            diagram_type = 'mindmap'
        elif first_line.startswith('timeline'):
            diagram_type = 'timeline'
        
        # Count nodes and edges (rough estimation)
        node_count = 0
        edge_count = 0
        
        for line in lines[1:]:
            line = line.strip()
            if not line or line.startswith('%'):
                continue
            
            # Count potential nodes (lines with brackets or standalone identifiers)
            if '[' in line and ']' in line:
                node_count += 1
            elif line and not any(arrow in line for arrow in ['-->', '---', '==>', '-.-', '->>', '->']):
                if not line.startswith(('style', 'class', 'click', 'subgraph', 'end')):
                    node_count += 1
            
            # Count edges (lines with arrows)
            if any(arrow in line for arrow in ['-->', '---', '==>', '-.-', '->>', '->']):
                edge_count += 1
        
        return {
            'type': diagram_type,
            'node_count': node_count,
            'edge_count': edge_count,
            'line_count': len(lines)
        }
