version: '3.8'

services:
  # Main Augment Agent Replica service
  augment-agent:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - MISTRAL_API_KEY=${MISTRAL_API_KEY}
      - GOOGLE_SEARCH_API_KEY=${GOOG<PERSON>_SEARCH_API_KEY}
      - GOOG<PERSON>_SEARCH_ENGINE_ID=${GOOGLE_SEARCH_ENGINE_ID}
      - DATABASE_URL=sqlite:///./data/agent.db
      - REDIS_URL=redis://redis:6379/0
      - LOG_LEVEL=INFO
      - WORKSPACE_ROOT=/app/workspace
    volumes:
      - ./workspace:/app/workspace
      - ./data:/app/data
      - ./logs:/app/logs
      - ./backups:/app/backups
    depends_on:
      - redis
      - elasticsearch
    restart: unless-stopped
    networks:
      - augment-network

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - augment-network

  # Elasticsearch for advanced code search
  elasticsearch:
    image: elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    restart: unless-stopped
    networks:
      - augment-network

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - augment-agent
    restart: unless-stopped
    networks:
      - augment-network
    profiles:
      - production

  # Prometheus for monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - augment-network
    profiles:
      - monitoring

  # Grafana for dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - augment-network
    profiles:
      - monitoring

volumes:
  redis_data:
  elasticsearch_data:
  prometheus_data:
  grafana_data:

networks:
  augment-network:
    driver: bridge
