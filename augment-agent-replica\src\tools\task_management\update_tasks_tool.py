"""
Update Tasks Tool - Exact replica of Augment Agent's update_tasks functionality
Update one or more tasks' properties (state, name, description)
"""

from typing import Dict, Any, List, Optional
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult
from .task_manager import task_manager, TaskState

class UpdateTasksTool(BaseTool):
    """
    Update one or more tasks' properties (state, name, description)
    Exact replica of original Augment Agent update_tasks tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.TASK_MANAGEMENT
    
    def _get_description(self) -> str:
        return """Update one or more tasks' properties (state, name, description). Can update a single task or multiple tasks in one call. Use this on complex sequences of work to plan, track progress, and manage work."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                # Single task update
                "task_id": {
                    "description": "The UUID of the task to update. Use this for single task updates. Either task_id or tasks array is required.",
                    "type": "string"
                },
                "name": {
                    "description": "New task name for single task update.",
                    "type": "string"
                },
                "description": {
                    "description": "New task description for single task update.",
                    "type": "string"
                },
                "state": {
                    "description": "New task state for single task update. Use NOT_STARTED for [ ], IN_PROGRESS for [/], CANCELLED for [-], COMPLETE for [x].",
                    "type": "string",
                    "enum": ["NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE"]
                },
                # Multiple task updates
                "tasks": {
                    "description": "Array of tasks to update. Use this for multiple task updates. Each task should have a task_id and the properties to update. Either task_id or tasks array is required.",
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "task_id": {
                                "description": "The UUID of the task to update.",
                                "type": "string"
                            },
                            "name": {
                                "description": "New task name.",
                                "type": "string"
                            },
                            "description": {
                                "description": "New task description.",
                                "type": "string"
                            },
                            "state": {
                                "description": "New task state. Use NOT_STARTED for [ ], IN_PROGRESS for [/], CANCELLED for [-], COMPLETE for [x].",
                                "type": "string",
                                "enum": ["NOT_STARTED", "IN_PROGRESS", "CANCELLED", "COMPLETE"]
                            }
                        },
                        "required": ["task_id"]
                    }
                }
            }
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the update_tasks tool"""
        try:
            # Get conversation ID from context if available
            conversation_id = kwargs.get('_context', {}).get('conversation_id', 'default')
            task_manager.set_conversation_id(conversation_id)
            
            # Determine if single task or multiple tasks
            updates_to_apply = []
            
            if 'tasks' in kwargs:
                # Multiple tasks mode
                tasks_data = kwargs['tasks']
                if not isinstance(tasks_data, list):
                    return ToolResult(
                        success=False,
                        error="tasks parameter must be an array"
                    )
                
                for task_data in tasks_data:
                    if not isinstance(task_data, dict):
                        return ToolResult(
                            success=False,
                            error="Each task in tasks array must be an object"
                        )
                    
                    if 'task_id' not in task_data:
                        return ToolResult(
                            success=False,
                            error="Each task must have 'task_id' property"
                        )
                    
                    updates_to_apply.append(task_data)
            
            else:
                # Single task mode
                task_id = kwargs.get('task_id')
                
                if not task_id:
                    return ToolResult(
                        success=False,
                        error="Either 'tasks' array or 'task_id' is required"
                    )
                
                update_data = {'task_id': task_id}
                
                # Add optional fields if provided
                for field in ['name', 'description', 'state']:
                    if field in kwargs:
                        update_data[field] = kwargs[field]
                
                updates_to_apply.append(update_data)
            
            # Apply all updates
            updated_tasks = []
            failed_updates = []
            
            for update_data in updates_to_apply:
                try:
                    task_id = update_data['task_id']
                    
                    # Parse state if provided
                    state = None
                    if 'state' in update_data:
                        try:
                            state = TaskState(update_data['state'])
                        except ValueError:
                            failed_updates.append({
                                'task_id': task_id,
                                'error': f"Invalid state value: {update_data['state']}. Must be one of: NOT_STARTED, IN_PROGRESS, COMPLETE, CANCELLED"
                            })
                            continue
                    
                    # Update the task
                    updated_task = task_manager.update_task(
                        task_id=task_id,
                        name=update_data.get('name'),
                        description=update_data.get('description'),
                        state=state
                    )
                    
                    if updated_task:
                        updated_tasks.append(updated_task.to_dict())
                    else:
                        failed_updates.append({
                            'task_id': task_id,
                            'error': f"Task with ID {task_id} not found"
                        })
                
                except Exception as e:
                    failed_updates.append({
                        'task_id': update_data.get('task_id', 'unknown'),
                        'error': f"Error updating task: {str(e)}"
                    })
            
            # Check if any updates failed
            if failed_updates and not updated_tasks:
                # All updates failed
                return ToolResult(
                    success=False,
                    error=f"All task updates failed. Errors: {failed_updates}"
                )
            
            # Get updated task list summary
            all_tasks = task_manager.get_tasks()
            summary = {
                'total_tasks': len(all_tasks),
                'tasks_updated': len(updated_tasks),
                'tasks_failed': len(failed_updates),
                'not_started': len([t for t in all_tasks if t.state.value == 'NOT_STARTED']),
                'in_progress': len([t for t in all_tasks if t.state.value == 'IN_PROGRESS']),
                'complete': len([t for t in all_tasks if t.state.value == 'COMPLETE']),
                'cancelled': len([t for t in all_tasks if t.state.value == 'CANCELLED'])
            }
            
            result_data = {
                'conversation_id': conversation_id,
                'updated_tasks': updated_tasks,
                'summary': summary,
                'message': f"Successfully updated {len(updated_tasks)} task(s)"
            }
            
            # Include failed updates if any
            if failed_updates:
                result_data['failed_updates'] = failed_updates
                result_data['message'] += f", {len(failed_updates)} failed"
            
            return ToolResult(
                success=True,
                data=result_data
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error updating tasks: {str(e)}"
            )
