"""
Main Augment Agent Replica Implementation
Replicates the exact behavior and decision-making patterns of the original Augment Agent
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Any, Optional, AsyncGenerator
from .mistral_client import MistralAIClient
from .tool_executor import ToolExecutor, tool_executor
from ..utils.config import Config
from ..utils.logger import setup_logger

logger = setup_logger(__name__)

class AugmentAgentReplica:
    """
    Main agent class that replicates Augment Agent's behavior patterns
    """
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or Config()
        self.mistral_client = MistralAIClient()
        self.tool_executor = tool_executor
        
        # Agent state
        self.conversation_history: List[Dict[str, str]] = []
        self.context = {
            'workspace_root': self.config.workspace_root,
            'current_task': None,
            'task_history': [],
            'memory': {},
            'user_preferences': {}
        }
        
        # Load and register all tools
        self._register_all_tools()
        
        # Agent behavior patterns (replicating original Augment Agent)
        self.behavior_patterns = {
            'information_first': True,  # Always gather context before changes
            'conservative_edits': True,  # Respect existing codebase patterns
            'batch_operations': True,   # Use efficient batch updates
            'test_driven': True,        # Suggest testing after changes
            'ask_permission': True,     # Ask before destructive actions
            'progress_tracking': True   # Use task management for complex work
        }
        
        logger.info("Augment Agent Replica initialized successfully")
    
    def _register_all_tools(self):
        """Register all available tools"""
        try:
            # Import and register file management tools
            from ..tools.file_management import (
                ViewTool, StrReplaceEditorTool, SaveFileTool, RemoveFilesTool
            )

            self.tool_executor.register_tool(ViewTool())
            self.tool_executor.register_tool(StrReplaceEditorTool())
            self.tool_executor.register_tool(SaveFileTool())
            self.tool_executor.register_tool(RemoveFilesTool())

            # Import and register code intelligence tools
            from ..tools.code_intelligence import (
                CodebaseRetrievalTool, DiagnosticsTool
            )

            self.tool_executor.register_tool(CodebaseRetrievalTool())
            self.tool_executor.register_tool(DiagnosticsTool())

            # Import and register process management tools
            from ..tools.process_management import (
                LaunchProcessTool, ReadProcessTool, WriteProcessTool,
                KillProcessTool, ListProcessesTool, ReadTerminalTool
            )

            self.tool_executor.register_tool(LaunchProcessTool())
            self.tool_executor.register_tool(ReadProcessTool())
            self.tool_executor.register_tool(WriteProcessTool())
            self.tool_executor.register_tool(KillProcessTool())
            self.tool_executor.register_tool(ListProcessesTool())
            self.tool_executor.register_tool(ReadTerminalTool())

            # Import and register web integration tools
            from ..tools.web_integration import (
                WebSearchTool, WebFetchTool, OpenBrowserTool
            )

            self.tool_executor.register_tool(WebSearchTool())
            self.tool_executor.register_tool(WebFetchTool())
            self.tool_executor.register_tool(OpenBrowserTool())

            # Import and register task management tools
            from ..tools.task_management import (
                ViewTasklistTool, AddTasksTool, UpdateTasksTool, ReorganizeTasklistTool
            )

            self.tool_executor.register_tool(ViewTasklistTool())
            self.tool_executor.register_tool(AddTasksTool())
            self.tool_executor.register_tool(UpdateTasksTool())
            self.tool_executor.register_tool(ReorganizeTasklistTool())

            # Import and register memory tools
            from ..tools.memory import (
                RememberTool, RenderMermaidTool
            )

            self.tool_executor.register_tool(RememberTool())
            self.tool_executor.register_tool(RenderMermaidTool())

            # Import and register content analysis tools
            from ..tools.content_analysis import (
                ViewRangeUntruncatedTool, SearchUntruncatedTool
            )

            self.tool_executor.register_tool(ViewRangeUntruncatedTool())
            self.tool_executor.register_tool(SearchUntruncatedTool())

            logger.info(f"Registered {len(self.tool_executor.tools)} tools across all categories")

        except Exception as e:
            logger.error(f"Error registering tools: {e}")
            raise
    
    async def chat(
        self, 
        message: str, 
        stream: bool = False,
        context: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Main chat interface that replicates Augment Agent's conversation flow
        """
        try:
            # Update context
            if context:
                self.context.update(context)
            
            # Add user message to history
            self.conversation_history.append({
                'role': 'user',
                'content': message,
                'timestamp': time.time()
            })
            
            # Analyze message and determine approach
            approach = await self._analyze_user_request(message)
            
            # Execute the conversation flow
            async for response_chunk in self._execute_conversation_flow(approach, stream):
                yield response_chunk
                
        except Exception as e:
            logger.error(f"Error in chat: {e}")
            yield {
                'type': 'error',
                'content': f"An error occurred: {str(e)}"
            }
    
    async def _analyze_user_request(self, message: str) -> Dict[str, Any]:
        """
        Analyze user request to determine the appropriate approach
        Replicates Augment Agent's decision-making process
        """
        analysis_prompt = f"""
        Analyze this user request and determine the approach:
        
        User message: "{message}"
        
        Consider:
        1. Does this require code changes? (information gathering first)
        2. Is this a complex multi-step task? (task management needed)
        3. Are there potentially destructive actions? (ask permission)
        4. Does this involve file operations?
        5. Is this a simple question or complex implementation?
        
        Respond with JSON:
        {{
            "complexity": "simple|moderate|complex",
            "requires_code_changes": true|false,
            "requires_task_management": true|false,
            "requires_information_gathering": true|false,
            "potentially_destructive": true|false,
            "primary_tools_needed": ["tool1", "tool2"],
            "approach": "direct_response|information_then_action|task_breakdown"
        }}
        """
        
        try:
            response = await self.mistral_client.chat_completion([
                {'role': 'user', 'content': analysis_prompt}
            ])
            
            # Parse JSON response
            analysis = json.loads(response['content'])
            return analysis
            
        except Exception as e:
            logger.warning(f"Error analyzing request, using default approach: {e}")
            # Default to safe approach
            return {
                "complexity": "moderate",
                "requires_code_changes": False,
                "requires_task_management": False,
                "requires_information_gathering": True,
                "potentially_destructive": False,
                "primary_tools_needed": ["view"],
                "approach": "information_then_action"
            }
    
    async def _execute_conversation_flow(
        self, 
        approach: Dict[str, Any], 
        stream: bool
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Execute the conversation flow based on the determined approach
        """
        try:
            # Get tool definitions for AI
            tool_definitions = self.tool_executor.get_tool_definitions_for_ai()
            
            # Prepare messages with system prompt and conversation history
            messages = self._prepare_messages_for_ai()
            
            # Get AI response with tool calling capability
            if stream:
                async for chunk in self.mistral_client.chat_completion(
                    messages, 
                    tools=tool_definitions, 
                    stream=True
                ):
                    if chunk.get('type') == 'content':
                        yield {
                            'type': 'content',
                            'content': chunk['content']
                        }
                    elif chunk.get('type') == 'tool_calls':
                        # Execute tools and yield results
                        async for tool_result in self._handle_tool_calls(chunk['tool_calls']):
                            yield tool_result
            else:
                response = await self.mistral_client.chat_completion(
                    messages, 
                    tools=tool_definitions, 
                    stream=False
                )
                
                # Yield content
                if response.get('content'):
                    yield {
                        'type': 'content',
                        'content': response['content']
                    }
                
                # Handle tool calls if present
                if response.get('tool_calls'):
                    async for tool_result in self._handle_tool_calls(response['tool_calls']):
                        yield tool_result
            
            # Add assistant response to history
            # (This would be more complex in a real implementation to handle tool calls properly)
            
        except Exception as e:
            logger.error(f"Error in conversation flow: {e}")
            yield {
                'type': 'error',
                'content': f"Error in conversation flow: {str(e)}"
            }
    
    def _prepare_messages_for_ai(self) -> List[Dict[str, str]]:
        """Prepare conversation messages for AI model"""
        # Start with system message (already included in MistralAIClient)
        messages = []
        
        # Add conversation history
        for msg in self.conversation_history[-10:]:  # Last 10 messages
            messages.append({
                'role': msg['role'],
                'content': msg['content']
            })
        
        return messages
    
    async def _handle_tool_calls(self, tool_calls: List[Dict[str, Any]]) -> AsyncGenerator[Dict[str, Any], None]:
        """Handle tool calls from AI model"""
        try:
            results = await self.tool_executor.handle_ai_tool_calls(tool_calls)
            
            for result in results:
                yield {
                    'type': 'tool_result',
                    'tool_name': result.tool_name,
                    'success': result.success,
                    'data': result.data,
                    'error': result.error,
                    'execution_time': result.execution_time
                }
                
        except Exception as e:
            logger.error(f"Error handling tool calls: {e}")
            yield {
                'type': 'error',
                'content': f"Error executing tools: {str(e)}"
            }
    
    async def execute_tool_directly(
        self, 
        tool_name: str, 
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute a tool directly (for API access)
        """
        try:
            result = await self.tool_executor.execute_tool(
                tool_name, 
                parameters, 
                self.context
            )
            
            return {
                'success': result.success,
                'data': result.data,
                'error': result.error,
                'execution_time': result.execution_time,
                'tool_name': result.tool_name
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'tool_name': tool_name
            }
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of all available tools"""
        return self.tool_executor.list_tools()
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get current agent status and statistics"""
        return {
            'status': 'active',
            'tools_registered': len(self.tool_executor.tools),
            'conversation_length': len(self.conversation_history),
            'execution_stats': self.tool_executor.get_execution_stats(),
            'context': {
                'workspace_root': self.context['workspace_root'],
                'current_task': self.context.get('current_task'),
                'memory_items': len(self.context.get('memory', {}))
            },
            'behavior_patterns': self.behavior_patterns,
            'model_info': self.mistral_client.get_model_info()
        }
    
    async def shutdown(self):
        """Graceful shutdown"""
        logger.info("Shutting down Augment Agent Replica")
        # Save conversation history, context, etc.
        # Clean up resources
