"""
Web Fetch Tool - Exact replica of Augment Agent's web-fetch functionality
Fetch webpage content and convert to Markdown without requiring API keys
"""

import requests
import re
from urllib.parse import urljoin, urlparse
from typing import Dict, Any, Optional
import html2text
from bs4 import BeautifulSoup
import logging

from ...core.tool_executor import BaseTool, ToolCategory, ToolResult

logger = logging.getLogger(__name__)

class WebFetchTool(BaseTool):
    """
    Fetches data from a webpage and converts it into Markdown
    Exact replica of original Augment Agent web-fetch tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.WEB_INTEGRATION
    
    def _get_description(self) -> str:
        return """Fetches data from a webpage and converts it into Markdown.

1. The tool takes in a URL and returns the content of the page in Markdown format;
2. If the return is not valid Markdown, it means the tool cannot successfully parse this page."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "url": {
                    "description": "The URL to fetch.",
                    "type": "string"
                }
            },
            "required": ["url"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute web fetch"""
        try:
            url = kwargs.get('url', '')
            
            if not url:
                return ToolResult(
                    success=False,
                    error="url parameter is required"
                )
            
            # Validate URL format
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                return ToolResult(
                    success=False,
                    error="Invalid URL format. URL must include protocol (http:// or https://)"
                )
            
            # Fetch the webpage
            try:
                response = await self._fetch_webpage(url)
            except Exception as e:
                return ToolResult(
                    success=False,
                    error=f"Failed to fetch webpage: {str(e)}"
                )
            
            # Convert to markdown
            try:
                markdown_content = self._convert_to_markdown(response['content'], url)
            except Exception as e:
                return ToolResult(
                    success=False,
                    error=f"Failed to convert to markdown: {str(e)}"
                )
            
            # Validate markdown
            if not markdown_content or len(markdown_content.strip()) < 10:
                return ToolResult(
                    success=False,
                    error="Could not extract meaningful content from the webpage"
                )
            
            return ToolResult(
                success=True,
                data={
                    'url': url,
                    'title': response.get('title', ''),
                    'content_type': response.get('content_type', ''),
                    'content_length': len(markdown_content),
                    'markdown': markdown_content,
                    'status_code': response.get('status_code', 200)
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error in web fetch: {str(e)}"
            )
    
    async def _fetch_webpage(self, url: str) -> Dict[str, Any]:
        """Fetch webpage content"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=30, allow_redirects=True)
            response.raise_for_status()
            
            # Check content type
            content_type = response.headers.get('content-type', '').lower()
            
            if 'text/html' not in content_type and 'application/xhtml' not in content_type:
                raise ValueError(f"Unsupported content type: {content_type}")
            
            # Get title from HTML
            title = self._extract_title(response.text)
            
            return {
                'content': response.text,
                'title': title,
                'content_type': content_type,
                'status_code': response.status_code,
                'url': response.url  # Final URL after redirects
            }
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"HTTP request failed: {str(e)}")
        except Exception as e:
            raise Exception(f"Failed to fetch webpage: {str(e)}")
    
    def _extract_title(self, html_content: str) -> str:
        """Extract title from HTML"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            title_tag = soup.find('title')
            if title_tag:
                return title_tag.get_text().strip()
        except Exception:
            pass
        
        # Fallback: regex extraction
        title_match = re.search(r'<title[^>]*>(.*?)</title>', html_content, re.IGNORECASE | re.DOTALL)
        if title_match:
            return html2text.html2text(title_match.group(1)).strip()
        
        return "Untitled"
    
    def _convert_to_markdown(self, html_content: str, base_url: str) -> str:
        """Convert HTML content to Markdown"""
        try:
            # First, clean up the HTML using BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove unwanted elements
            unwanted_tags = [
                'script', 'style', 'nav', 'header', 'footer', 'aside',
                'iframe', 'object', 'embed', 'form', 'input', 'button',
                'select', 'textarea', 'noscript', 'meta', 'link'
            ]
            
            for tag in unwanted_tags:
                for element in soup.find_all(tag):
                    element.decompose()
            
            # Remove elements with common ad/navigation classes
            unwanted_classes = [
                'advertisement', 'ads', 'ad', 'sidebar', 'navigation',
                'nav', 'menu', 'footer', 'header', 'social', 'share',
                'comment', 'popup', 'modal', 'overlay'
            ]
            
            for class_name in unwanted_classes:
                for element in soup.find_all(class_=re.compile(class_name, re.I)):
                    element.decompose()
            
            # Try to find main content area
            main_content = None
            
            # Look for common content containers
            content_selectors = [
                'main', 'article', '[role="main"]', '.content', '.main-content',
                '.post-content', '.entry-content', '.article-content', '#content',
                '#main', '.container .content', '.page-content'
            ]
            
            for selector in content_selectors:
                try:
                    element = soup.select_one(selector)
                    if element and len(element.get_text().strip()) > 200:
                        main_content = element
                        break
                except Exception:
                    continue
            
            # If no main content found, use body
            if not main_content:
                main_content = soup.find('body') or soup
            
            # Convert to markdown using html2text
            h = html2text.HTML2Text()
            h.ignore_links = False
            h.ignore_images = False
            h.ignore_emphasis = False
            h.body_width = 0  # Don't wrap lines
            h.unicode_snob = True
            h.escape_snob = True
            
            # Convert relative URLs to absolute
            self._convert_relative_urls(main_content, base_url)
            
            # Convert to markdown
            markdown = h.handle(str(main_content))
            
            # Clean up the markdown
            markdown = self._clean_markdown(markdown)
            
            return markdown
            
        except Exception as e:
            logger.error(f"Error converting HTML to markdown: {e}")
            # Fallback: simple text extraction
            try:
                soup = BeautifulSoup(html_content, 'html.parser')
                text = soup.get_text()
                # Clean up whitespace
                lines = [line.strip() for line in text.split('\n') if line.strip()]
                return '\n\n'.join(lines)
            except Exception:
                raise Exception("Failed to extract text content from HTML")
    
    def _convert_relative_urls(self, soup_element, base_url: str):
        """Convert relative URLs to absolute URLs"""
        try:
            # Convert relative links
            for link in soup_element.find_all('a', href=True):
                href = link['href']
                if href and not href.startswith(('http://', 'https://', 'mailto:', 'tel:')):
                    link['href'] = urljoin(base_url, href)
            
            # Convert relative images
            for img in soup_element.find_all('img', src=True):
                src = img['src']
                if src and not src.startswith(('http://', 'https://', 'data:')):
                    img['src'] = urljoin(base_url, src)
        except Exception as e:
            logger.warning(f"Error converting relative URLs: {e}")
    
    def _clean_markdown(self, markdown: str) -> str:
        """Clean up markdown content"""
        # Remove excessive whitespace
        markdown = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown)
        
        # Remove empty links
        markdown = re.sub(r'\[\s*\]\([^)]*\)', '', markdown)
        
        # Remove excessive asterisks/underscores
        markdown = re.sub(r'\*{3,}', '**', markdown)
        markdown = re.sub(r'_{3,}', '__', markdown)
        
        # Clean up list formatting
        markdown = re.sub(r'\n\s*\*\s*\n', '\n', markdown)
        
        # Remove trailing whitespace from lines
        lines = [line.rstrip() for line in markdown.split('\n')]
        markdown = '\n'.join(lines)
        
        # Limit consecutive empty lines
        markdown = re.sub(r'\n{4,}', '\n\n\n', markdown)
        
        return markdown.strip()
