#!/usr/bin/env python3
"""
Simple server launcher for Augment Agent Replica
Fixes import issues and starts the server properly
"""

import os
import sys
import uvicorn
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

def main():
    """Main server launcher"""
    try:
        print("🤖 Starting Augment Agent Replica Server...")
        
        # Import after path setup
        from core.agent import AugmentAgentReplica
        from utils.config import Config
        from api.server import create_app
        
        # Initialize configuration
        config = Config()
        print(f"📋 Configuration loaded")
        
        # Initialize agent
        print("🔧 Initializing agent...")
        agent = AugmentAgentReplica(config)
        print(f"✅ Agent initialized with {len(agent.get_available_tools())} tools")
        
        # Create FastAPI app
        print("🌐 Creating web application...")
        app = create_app(agent, config)
        print("✅ Web application created")
        
        # Start server
        print(f"🚀 Starting server on {config.host}:{config.port}")
        print(f"📡 Server will be available at: http://{config.host}:{config.port}")
        print(f"📋 API docs at: http://{config.host}:{config.port}/docs")
        print("🛑 Press Ctrl+C to stop the server")
        print("-" * 50)
        
        uvicorn.run(
            app,
            host=config.host,
            port=config.port,
            log_level="info",
            reload=False
        )
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
