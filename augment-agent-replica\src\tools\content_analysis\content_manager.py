"""
Content Manager - Central management for content analysis and storage
Handles large content that may be truncated in regular responses
"""

import hashlib
import time
import sqlite3
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class ContentReference:
    """Represents a reference to stored content"""
    reference_id: str
    content_type: str
    title: str
    total_lines: int
    created_at: float
    metadata: Dict[str, Any]

class ContentManager:
    """Central manager for content analysis and storage"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or ":memory:"
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database for content storage"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS content_storage (
                    reference_id TEXT PRIMARY KEY,
                    content_type TEXT NOT NULL,
                    title TEXT,
                    content TEXT NOT NULL,
                    total_lines INTEGER,
                    created_at REAL,
                    metadata TEXT,
                    conversation_id TEXT
                )
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_content_conversation 
                ON content_storage(conversation_id)
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_content_created 
                ON content_storage(created_at)
            ''')
    
    def store_content(
        self,
        content: str,
        content_type: str,
        title: str = "",
        metadata: Optional[Dict[str, Any]] = None,
        conversation_id: str = "default"
    ) -> str:
        """Store content and return reference ID"""
        
        # Generate reference ID
        content_hash = hashlib.md5(content.encode()).hexdigest()[:12]
        timestamp = str(int(time.time()))
        reference_id = f"ref_{timestamp}_{content_hash}"
        
        # Count lines
        total_lines = len(content.split('\n'))
        
        # Store in database
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO content_storage 
                    (reference_id, content_type, title, content, total_lines, created_at, metadata, conversation_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    reference_id,
                    content_type,
                    title,
                    content,
                    total_lines,
                    time.time(),
                    json.dumps(metadata or {}),
                    conversation_id
                ))
            
            logger.info(f"Stored content with reference ID: {reference_id}")
            return reference_id
        
        except Exception as e:
            logger.error(f"Error storing content: {e}")
            raise
    
    def get_content(self, reference_id: str) -> Optional[str]:
        """Get full content by reference ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'SELECT content FROM content_storage WHERE reference_id = ?',
                    (reference_id,)
                )
                row = cursor.fetchone()
                return row[0] if row else None
        
        except Exception as e:
            logger.error(f"Error getting content {reference_id}: {e}")
            return None
    
    def get_content_reference(self, reference_id: str) -> Optional[ContentReference]:
        """Get content reference metadata"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    'SELECT * FROM content_storage WHERE reference_id = ?',
                    (reference_id,)
                )
                row = cursor.fetchone()
                
                if row:
                    return ContentReference(
                        reference_id=row['reference_id'],
                        content_type=row['content_type'],
                        title=row['title'],
                        total_lines=row['total_lines'],
                        created_at=row['created_at'],
                        metadata=json.loads(row['metadata'])
                    )
        
        except Exception as e:
            logger.error(f"Error getting content reference {reference_id}: {e}")
        
        return None
    
    def get_content_range(
        self,
        reference_id: str,
        start_line: int,
        end_line: int
    ) -> Optional[str]:
        """Get specific line range from content"""
        content = self.get_content(reference_id)
        if not content:
            return None
        
        lines = content.split('\n')
        
        # Convert to 0-based indexing
        start_idx = max(0, start_line - 1)
        end_idx = min(len(lines), end_line)
        
        if start_idx >= len(lines):
            return ""
        
        selected_lines = lines[start_idx:end_idx]
        return '\n'.join(selected_lines)
    
    def search_content(
        self,
        reference_id: str,
        search_term: str,
        context_lines: int = 2,
        case_sensitive: bool = False
    ) -> List[Dict[str, Any]]:
        """Search for term within content and return matches with context"""
        content = self.get_content(reference_id)
        if not content:
            return []
        
        lines = content.split('\n')
        matches = []
        
        # Prepare search term
        if not case_sensitive:
            search_term_lower = search_term.lower()
        
        # Find all matching lines
        for line_num, line in enumerate(lines, 1):
            search_line = line.lower() if not case_sensitive else line
            search_target = search_term_lower if not case_sensitive else search_term
            
            if search_target in search_line:
                # Get context
                start_context = max(0, line_num - 1 - context_lines)
                end_context = min(len(lines), line_num + context_lines)
                
                context_lines_list = []
                for i in range(start_context, end_context):
                    context_lines_list.append({
                        'line_number': i + 1,
                        'content': lines[i],
                        'is_match': i == line_num - 1
                    })
                
                matches.append({
                    'line_number': line_num,
                    'line_content': line,
                    'context': context_lines_list,
                    'match_positions': self._find_match_positions(line, search_term, case_sensitive)
                })
        
        return matches
    
    def _find_match_positions(
        self,
        line: str,
        search_term: str,
        case_sensitive: bool
    ) -> List[Tuple[int, int]]:
        """Find all positions where search term appears in line"""
        positions = []
        search_line = line if case_sensitive else line.lower()
        search_target = search_term if case_sensitive else search_term.lower()
        
        start = 0
        while True:
            pos = search_line.find(search_target, start)
            if pos == -1:
                break
            positions.append((pos, pos + len(search_term)))
            start = pos + 1
        
        return positions
    
    def list_content_references(
        self,
        conversation_id: str = "default",
        limit: int = 50
    ) -> List[ContentReference]:
        """List content references for a conversation"""
        references = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute('''
                    SELECT * FROM content_storage 
                    WHERE conversation_id = ?
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (conversation_id, limit))
                
                for row in cursor.fetchall():
                    references.append(ContentReference(
                        reference_id=row['reference_id'],
                        content_type=row['content_type'],
                        title=row['title'],
                        total_lines=row['total_lines'],
                        created_at=row['created_at'],
                        metadata=json.loads(row['metadata'])
                    ))
        
        except Exception as e:
            logger.error(f"Error listing content references: {e}")
        
        return references
    
    def delete_content(self, reference_id: str) -> bool:
        """Delete stored content"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'DELETE FROM content_storage WHERE reference_id = ?',
                    (reference_id,)
                )
                return cursor.rowcount > 0
        
        except Exception as e:
            logger.error(f"Error deleting content {reference_id}: {e}")
            return False
    
    def cleanup_old_content(self, max_age_hours: int = 24) -> int:
        """Clean up old content references"""
        try:
            cutoff_time = time.time() - (max_age_hours * 3600)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'DELETE FROM content_storage WHERE created_at < ?',
                    (cutoff_time,)
                )
                
                deleted_count = cursor.rowcount
                logger.info(f"Cleaned up {deleted_count} old content references")
                return deleted_count
        
        except Exception as e:
            logger.error(f"Error cleaning up content: {e}")
            return 0
    
    def get_content_stats(self, conversation_id: str = "default") -> Dict[str, Any]:
        """Get statistics about stored content"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT 
                        COUNT(*) as total_references,
                        SUM(total_lines) as total_lines,
                        AVG(total_lines) as avg_lines_per_reference,
                        MIN(created_at) as oldest_reference,
                        MAX(created_at) as newest_reference
                    FROM content_storage 
                    WHERE conversation_id = ?
                ''', (conversation_id,))
                
                row = cursor.fetchone()
                
                return {
                    'total_references': row[0],
                    'total_lines': row[1] or 0,
                    'average_lines_per_reference': row[2] or 0.0,
                    'oldest_reference': row[3],
                    'newest_reference': row[4],
                    'conversation_id': conversation_id
                }
        
        except Exception as e:
            logger.error(f"Error getting content stats: {e}")
            return {
                'total_references': 0,
                'total_lines': 0,
                'average_lines_per_reference': 0.0,
                'oldest_reference': None,
                'newest_reference': None,
                'conversation_id': conversation_id
            }

# Global content manager instance
content_manager = ContentManager()
