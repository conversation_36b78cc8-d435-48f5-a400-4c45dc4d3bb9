"""
String Replace Editor Tool - Exact replica of Augment Agent's str-replace-editor functionality
Supports precise file editing with line number targeting and multiple replacements
"""

import os
import shutil
from typing import Dict, Any, List, Optional
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult

class StrReplaceEditorTool(BaseTool):
    """
    Tool for editing files with precise string replacement and insertion
    Exact replica of original Augment Agent str-replace-editor tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.FILE_MANAGEMENT
    
    def _get_description(self) -> str:
        return """Tool for editing files.
* `path` is a file path relative to the workspace root
* `insert` and `str_replace` commands output a snippet of the edited section for each entry. This snippet reflects the final state of the file after all edits and IDE auto-formatting have been applied.
* Generate `instruction_reminder` first to remind yourself to limit the edits to at most 150 lines.

Notes for using the `str_replace` command:
* Specify `old_str_1`, `new_str_1`, `old_str_start_line_number_1` and `old_str_end_line_number_1` properties for the first replacement, `old_str_2`, `new_str_2`, `old_str_start_line_number_2` and `old_str_end_line_number_2` for the second replacement, and so on
* The `old_str_start_line_number_1` and `old_str_end_line_number_1` parameters are 1-based line numbers
* Both `old_str_start_line_number_1` and `old_str_end_line_number_1` are INCLUSIVE
* The `old_str_1` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!
* Empty `old_str_1` is allowed only when the file is empty or contains only whitespaces
* It is important to specify `old_str_start_line_number_1` and `old_str_end_line_number_1` to disambiguate between multiple occurrences of `old_str_1` in the file
* Make sure that `old_str_start_line_number_1` and `old_str_end_line_number_1` do not overlap with other `old_str_start_line_number_2` and `old_str_end_line_number_2` entries
* The `new_str_1` parameter should contain the edited lines that should replace the `old_str_1`. Can be an empty string to delete content
* To make multiple replacements in one tool call add multiple sets of replacement parameters. For example, `old_str_1`, `new_str_1`, `old_str_start_line_number_1` and `old_str_end_line_number_1` properties for the first replacement, `old_str_2`, `new_str_2`, `old_str_start_line_number_2`, `old_str_end_line_number_2` for the second replacement, etc.

Notes for using the `insert` command:
* Specify `insert_line_1` and `new_str_1` properties for the first insertion, `insert_line_2` and `new_str_2` for the second insertion, and so on
* The `insert_line_1` parameter specifies the line number after which to insert the new string
* The `insert_line_1` parameter is 1-based line number
* To insert at the very beginning of the file, use `insert_line_1: 0`
* To make multiple insertions in one tool call add multiple sets of insertion parameters. For example, `insert_line_1` and `new_str_1` properties for the first insertion, `insert_line_2` and `new_str_2` for the second insertion, etc.

IMPORTANT:
* This is the only tool you should use for editing files.
* If it fails try your best to fix inputs and retry.
* DO NOT fall back to removing the whole file and recreating it from scratch.
* DO NOT use sed or any other command line tools for editing files.
* Try to fit as many edits in one tool call as possible
* Use the view tool to read files before editing them."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "command": {
                    "description": "The commands to run. Allowed options are: 'str_replace', 'insert'.",
                    "enum": ["str_replace", "insert"],
                    "type": "string"
                },
                "path": {
                    "description": "Full path to file relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.",
                    "type": "string"
                },
                "instruction_reminder": {
                    "description": "Reminder to limit edits to at most 150 lines. Should be exactly this string: 'ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH.'",
                    "type": "string"
                },
                # String replacement parameters (multiple supported)
                "old_str_1": {"description": "Required parameter of `str_replace` command containing the string in `path` to replace.", "type": "string"},
                "new_str_1": {"description": "Required parameter of `str_replace` command containing the new string. Can be an empty string to delete content. Required parameter of `insert` command containing the string to insert.", "type": "string"},
                "old_str_start_line_number_1": {"description": "The line number of the first line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.", "type": "integer"},
                "old_str_end_line_number_1": {"description": "The line number of the last line of `old_str_1` in the file. This is used to disambiguate between multiple occurrences of `old_str_1` in the file.", "type": "integer"},
                # Insert parameters (multiple supported)
                "insert_line_1": {"description": "Required parameter of `insert` command. The line number after which to insert the new string. This line number is relative to the state of the file before any insertions in the current tool call have been applied.", "type": "integer"},
                # Additional replacement/insertion parameters (2, 3, 4, etc.) would be dynamically handled
            },
            "required": ["command", "path", "instruction_reminder"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the str-replace-editor tool"""
        try:
            command = kwargs.get('command')
            path = kwargs.get('path', '')
            instruction_reminder = kwargs.get('instruction_reminder', '')
            
            # Validate instruction reminder
            expected_reminder = 'ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH.'
            if instruction_reminder != expected_reminder:
                return ToolResult(
                    success=False,
                    error=f"Invalid instruction_reminder. Expected: '{expected_reminder}'"
                )
            
            # Get workspace root from context
            workspace_root = kwargs.get('_context', {}).get('workspace_root', os.getcwd())
            full_path = os.path.join(workspace_root, path)
            
            if command == 'str_replace':
                return await self._handle_str_replace(full_path, kwargs)
            elif command == 'insert':
                return await self._handle_insert(full_path, kwargs)
            else:
                return ToolResult(
                    success=False,
                    error=f"Invalid command: {command}. Must be 'str_replace' or 'insert'"
                )
                
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error in str-replace-editor tool: {str(e)}"
            )
    
    async def _handle_str_replace(self, file_path: str, kwargs: Dict[str, Any]) -> ToolResult:
        """Handle string replacement operations"""
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error=f"File not found: {file_path}"
                )
            
            # Read original file
            with open(file_path, 'r', encoding='utf-8') as f:
                original_lines = f.readlines()
            
            # Create backup
            backup_path = f"{file_path}.backup"
            shutil.copy2(file_path, backup_path)
            
            # Collect all replacement operations
            replacements = self._collect_replacements(kwargs)
            
            if not replacements:
                return ToolResult(
                    success=False,
                    error="No replacement operations found"
                )
            
            # Sort replacements by start line (descending) to avoid line number shifts
            replacements.sort(key=lambda x: x['start_line'], reverse=True)
            
            # Validate replacements don't overlap
            if not self._validate_no_overlaps(replacements):
                return ToolResult(
                    success=False,
                    error="Replacement ranges overlap"
                )
            
            # Apply replacements
            modified_lines = original_lines.copy()
            applied_replacements = []
            
            for replacement in replacements:
                result = self._apply_replacement(modified_lines, replacement)
                if not result['success']:
                    # Restore from backup
                    shutil.copy2(backup_path, file_path)
                    os.remove(backup_path)
                    return ToolResult(
                        success=False,
                        error=result['error']
                    )
                applied_replacements.append(result)
            
            # Write modified file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(modified_lines)
            
            # Remove backup
            os.remove(backup_path)
            
            # Generate output snippets
            snippets = self._generate_snippets(modified_lines, applied_replacements)
            
            return ToolResult(
                success=True,
                data={
                    'operation': 'str_replace',
                    'file_path': file_path,
                    'replacements_applied': len(applied_replacements),
                    'snippets': snippets,
                    'total_lines': len(modified_lines)
                }
            )
            
        except Exception as e:
            # Restore from backup if it exists
            backup_path = f"{file_path}.backup"
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, file_path)
                os.remove(backup_path)
            
            return ToolResult(
                success=False,
                error=f"Error in string replacement: {str(e)}"
            )
    
    async def _handle_insert(self, file_path: str, kwargs: Dict[str, Any]) -> ToolResult:
        """Handle insertion operations"""
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    success=False,
                    error=f"File not found: {file_path}"
                )
            
            # Read original file
            with open(file_path, 'r', encoding='utf-8') as f:
                original_lines = f.readlines()
            
            # Create backup
            backup_path = f"{file_path}.backup"
            shutil.copy2(file_path, backup_path)
            
            # Collect all insertion operations
            insertions = self._collect_insertions(kwargs)
            
            if not insertions:
                return ToolResult(
                    success=False,
                    error="No insertion operations found"
                )
            
            # Sort insertions by line number (descending) to avoid line number shifts
            insertions.sort(key=lambda x: x['line'], reverse=True)
            
            # Apply insertions
            modified_lines = original_lines.copy()
            applied_insertions = []
            
            for insertion in insertions:
                result = self._apply_insertion(modified_lines, insertion)
                if not result['success']:
                    # Restore from backup
                    shutil.copy2(backup_path, file_path)
                    os.remove(backup_path)
                    return ToolResult(
                        success=False,
                        error=result['error']
                    )
                applied_insertions.append(result)
            
            # Write modified file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(modified_lines)
            
            # Remove backup
            os.remove(backup_path)
            
            # Generate output snippets
            snippets = self._generate_insertion_snippets(modified_lines, applied_insertions)
            
            return ToolResult(
                success=True,
                data={
                    'operation': 'insert',
                    'file_path': file_path,
                    'insertions_applied': len(applied_insertions),
                    'snippets': snippets,
                    'total_lines': len(modified_lines)
                }
            )
            
        except Exception as e:
            # Restore from backup if it exists
            backup_path = f"{file_path}.backup"
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, file_path)
                os.remove(backup_path)
            
            return ToolResult(
                success=False,
                error=f"Error in insertion: {str(e)}"
            )

    def _collect_replacements(self, kwargs: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Collect all replacement operations from kwargs"""
        replacements = []
        i = 1

        while f'old_str_{i}' in kwargs:
            replacement = {
                'old_str': kwargs[f'old_str_{i}'],
                'new_str': kwargs[f'new_str_{i}'],
                'start_line': kwargs[f'old_str_start_line_number_{i}'],
                'end_line': kwargs[f'old_str_end_line_number_{i}'],
                'index': i
            }
            replacements.append(replacement)
            i += 1

        return replacements

    def _collect_insertions(self, kwargs: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Collect all insertion operations from kwargs"""
        insertions = []
        i = 1

        while f'insert_line_{i}' in kwargs:
            insertion = {
                'line': kwargs[f'insert_line_{i}'],
                'content': kwargs[f'new_str_{i}'],
                'index': i
            }
            insertions.append(insertion)
            i += 1

        return insertions

    def _validate_no_overlaps(self, replacements: List[Dict[str, Any]]) -> bool:
        """Validate that replacement ranges don't overlap"""
        for i, repl1 in enumerate(replacements):
            for j, repl2 in enumerate(replacements[i+1:], i+1):
                # Check if ranges overlap
                if not (repl1['end_line'] < repl2['start_line'] or repl2['end_line'] < repl1['start_line']):
                    return False
        return True

    def _apply_replacement(self, lines: List[str], replacement: Dict[str, Any]) -> Dict[str, Any]:
        """Apply a single replacement operation"""
        try:
            start_line = replacement['start_line'] - 1  # Convert to 0-based
            end_line = replacement['end_line'] - 1      # Convert to 0-based
            old_str = replacement['old_str']
            new_str = replacement['new_str']

            # Validate line numbers
            if start_line < 0 or end_line >= len(lines) or start_line > end_line:
                return {
                    'success': False,
                    'error': f"Invalid line range: {start_line+1}-{end_line+1}"
                }

            # Extract the text to replace
            actual_text = ''.join(lines[start_line:end_line+1]).rstrip('\n')
            expected_text = old_str.rstrip('\n')

            # Check if the text matches
            if actual_text != expected_text:
                return {
                    'success': False,
                    'error': f"Text mismatch at lines {start_line+1}-{end_line+1}. Expected: '{expected_text}', Found: '{actual_text}'"
                }

            # Prepare new content
            new_lines = []
            if new_str:
                new_content_lines = new_str.split('\n')
                for line in new_content_lines:
                    new_lines.append(line + '\n')
                # Remove trailing newline from last line if original didn't have it
                if new_lines and not new_str.endswith('\n'):
                    new_lines[-1] = new_lines[-1].rstrip('\n')

            # Replace the lines
            lines[start_line:end_line+1] = new_lines

            return {
                'success': True,
                'start_line': start_line + 1,
                'end_line': start_line + len(new_lines),
                'lines_added': len(new_lines) - (end_line - start_line + 1),
                'replacement': replacement
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"Error applying replacement: {str(e)}"
            }

    def _apply_insertion(self, lines: List[str], insertion: Dict[str, Any]) -> Dict[str, Any]:
        """Apply a single insertion operation"""
        try:
            insert_line = insertion['line']  # 1-based, 0 means beginning
            content = insertion['content']

            # Validate line number
            if insert_line < 0 or insert_line > len(lines):
                return {
                    'success': False,
                    'error': f"Invalid insertion line: {insert_line}"
                }

            # Prepare content to insert
            insert_lines = []
            if content:
                content_lines = content.split('\n')
                for line in content_lines:
                    insert_lines.append(line + '\n')
                # Remove trailing newline from last line if content didn't have it
                if insert_lines and not content.endswith('\n'):
                    insert_lines[-1] = insert_lines[-1].rstrip('\n')

            # Insert the lines
            lines[insert_line:insert_line] = insert_lines

            return {
                'success': True,
                'insert_line': insert_line,
                'lines_inserted': len(insert_lines),
                'insertion': insertion
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"Error applying insertion: {str(e)}"
            }

    def _generate_snippets(self, lines: List[str], applied_replacements: List[Dict[str, Any]]) -> List[str]:
        """Generate output snippets showing the edited sections"""
        snippets = []

        for replacement in applied_replacements:
            start_line = replacement['start_line']
            end_line = replacement['end_line']

            # Show context around the change
            context_start = max(0, start_line - 3)
            context_end = min(len(lines), end_line + 3)

            snippet_lines = []
            for i in range(context_start, context_end):
                line_num = i + 1
                line_content = lines[i].rstrip()
                snippet_lines.append(f"{line_num:6d}\t{line_content}")

            snippets.append('\n'.join(snippet_lines))

        return snippets

    def _generate_insertion_snippets(self, lines: List[str], applied_insertions: List[Dict[str, Any]]) -> List[str]:
        """Generate output snippets showing the inserted sections"""
        snippets = []

        for insertion in applied_insertions:
            insert_line = insertion['insert_line']
            lines_inserted = insertion['lines_inserted']

            # Show context around the insertion
            context_start = max(0, insert_line - 2)
            context_end = min(len(lines), insert_line + lines_inserted + 2)

            snippet_lines = []
            for i in range(context_start, context_end):
                line_num = i + 1
                line_content = lines[i].rstrip()
                snippet_lines.append(f"{line_num:6d}\t{line_content}")

            snippets.append('\n'.join(snippet_lines))

        return snippets
