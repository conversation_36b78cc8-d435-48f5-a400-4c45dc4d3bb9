"""
Codebase Retrieval Tool - Exact replica of Augment Agent's codebase-retrieval functionality
Advanced semantic code search with real-time indexing and cross-language support
"""

import os
import ast
import re
import json
import hashlib
import sqlite3
from pathlib import Path
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass
import time
import logging

from ...core.tool_executor import BaseTool, ToolCategory, ToolResult

logger = logging.getLogger(__name__)

@dataclass
class CodeSymbol:
    """Represents a code symbol (class, function, variable, etc.)"""
    name: str
    type: str  # 'class', 'function', 'variable', 'import', etc.
    file_path: str
    line_number: int
    content: str
    docstring: Optional[str] = None
    parent: Optional[str] = None
    language: str = 'python'

@dataclass
class SearchResult:
    """Represents a search result"""
    symbol: CodeSymbol
    relevance_score: float
    context: str
    snippet: str

class CodebaseIndexer:
    """Indexes codebase for semantic search"""
    
    def __init__(self, workspace_root: str, index_path: str):
        self.workspace_root = workspace_root
        self.index_path = index_path
        self.db_path = os.path.join(index_path, 'codebase.db')
        self._ensure_index_dir()
        self._init_database()
        
        # File extensions to index
        self.supported_extensions = {
            '.py': 'python',
            '.js': 'javascript', 
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.hpp': 'cpp',
            '.cs': 'csharp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.sql': 'sql',
            '.md': 'markdown',
            '.txt': 'text',
            '.json': 'json',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.xml': 'xml',
            '.html': 'html',
            '.css': 'css'
        }
    
    def _ensure_index_dir(self):
        """Ensure index directory exists"""
        os.makedirs(self.index_path, exist_ok=True)
    
    def _init_database(self):
        """Initialize SQLite database for indexing"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS symbols (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    line_number INTEGER,
                    content TEXT,
                    docstring TEXT,
                    parent TEXT,
                    language TEXT,
                    file_hash TEXT,
                    indexed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(name, type, file_path, line_number)
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS file_metadata (
                    file_path TEXT PRIMARY KEY,
                    file_hash TEXT,
                    last_modified TIMESTAMP,
                    indexed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create indexes for faster searching
            conn.execute('CREATE INDEX IF NOT EXISTS idx_symbols_name ON symbols(name)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_symbols_type ON symbols(type)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_symbols_file ON symbols(file_path)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_symbols_content ON symbols(content)')
    
    def _get_file_hash(self, file_path: str) -> str:
        """Get hash of file content"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return ""
    
    def _should_reindex_file(self, file_path: str) -> bool:
        """Check if file needs reindexing"""
        try:
            current_hash = self._get_file_hash(file_path)
            current_mtime = os.path.getmtime(file_path)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'SELECT file_hash, last_modified FROM file_metadata WHERE file_path = ?',
                    (file_path,)
                )
                result = cursor.fetchone()
                
                if not result:
                    return True
                
                stored_hash, stored_mtime = result
                return current_hash != stored_hash or current_mtime != stored_mtime
                
        except Exception:
            return True
    
    def index_file(self, file_path: str) -> List[CodeSymbol]:
        """Index a single file"""
        try:
            if not self._should_reindex_file(file_path):
                return []
            
            ext = Path(file_path).suffix.lower()
            language = self.supported_extensions.get(ext, 'text')
            
            symbols = []
            
            if language == 'python':
                symbols = self._index_python_file(file_path)
            elif language in ['javascript', 'typescript']:
                symbols = self._index_js_file(file_path, language)
            else:
                symbols = self._index_generic_file(file_path, language)
            
            # Store symbols in database
            self._store_symbols(symbols, file_path)
            
            return symbols
            
        except Exception as e:
            logger.error(f"Error indexing file {file_path}: {e}")
            return []
    
    def _index_python_file(self, file_path: str) -> List[CodeSymbol]:
        """Index Python file using AST"""
        symbols = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                symbol = None
                
                if isinstance(node, ast.ClassDef):
                    symbol = CodeSymbol(
                        name=node.name,
                        type='class',
                        file_path=file_path,
                        line_number=node.lineno,
                        content=ast.get_source_segment(content, node) or "",
                        docstring=ast.get_docstring(node),
                        language='python'
                    )
                
                elif isinstance(node, ast.FunctionDef):
                    parent = None
                    # Find parent class if method
                    for parent_node in ast.walk(tree):
                        if (isinstance(parent_node, ast.ClassDef) and 
                            node.lineno > parent_node.lineno and
                            node.lineno < getattr(parent_node, 'end_lineno', float('inf'))):
                            parent = parent_node.name
                            break
                    
                    symbol = CodeSymbol(
                        name=node.name,
                        type='method' if parent else 'function',
                        file_path=file_path,
                        line_number=node.lineno,
                        content=ast.get_source_segment(content, node) or "",
                        docstring=ast.get_docstring(node),
                        parent=parent,
                        language='python'
                    )
                
                elif isinstance(node, ast.Import):
                    for alias in node.names:
                        symbol = CodeSymbol(
                            name=alias.name,
                            type='import',
                            file_path=file_path,
                            line_number=node.lineno,
                            content=f"import {alias.name}",
                            language='python'
                        )
                        symbols.append(symbol)
                    continue
                
                elif isinstance(node, ast.ImportFrom):
                    for alias in node.names:
                        symbol = CodeSymbol(
                            name=alias.name,
                            type='import',
                            file_path=file_path,
                            line_number=node.lineno,
                            content=f"from {node.module} import {alias.name}",
                            language='python'
                        )
                        symbols.append(symbol)
                    continue
                
                if symbol:
                    symbols.append(symbol)
        
        except Exception as e:
            logger.error(f"Error parsing Python file {file_path}: {e}")
        
        return symbols
    
    def _index_js_file(self, file_path: str, language: str) -> List[CodeSymbol]:
        """Index JavaScript/TypeScript file using regex patterns"""
        symbols = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # Patterns for different JS/TS constructs
            patterns = {
                'class': r'class\s+(\w+)',
                'function': r'function\s+(\w+)',
                'arrow_function': r'const\s+(\w+)\s*=\s*\([^)]*\)\s*=>',
                'method': r'(\w+)\s*\([^)]*\)\s*{',
                'variable': r'(?:const|let|var)\s+(\w+)',
                'import': r'import\s+.*?from\s+[\'"]([^\'"]+)[\'"]',
                'export': r'export\s+(?:default\s+)?(?:class|function|const|let|var)\s+(\w+)'
            }
            
            for line_num, line in enumerate(lines, 1):
                for symbol_type, pattern in patterns.items():
                    matches = re.finditer(pattern, line)
                    for match in matches:
                        name = match.group(1)
                        symbol = CodeSymbol(
                            name=name,
                            type=symbol_type,
                            file_path=file_path,
                            line_number=line_num,
                            content=line.strip(),
                            language=language
                        )
                        symbols.append(symbol)
        
        except Exception as e:
            logger.error(f"Error parsing {language} file {file_path}: {e}")
        
        return symbols
    
    def _index_generic_file(self, file_path: str, language: str) -> List[CodeSymbol]:
        """Index generic text file for keywords and patterns"""
        symbols = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # Extract meaningful patterns based on language
            if language == 'markdown':
                # Extract headers
                for line_num, line in enumerate(lines, 1):
                    if line.startswith('#'):
                        level = len(line) - len(line.lstrip('#'))
                        title = line.lstrip('# ').strip()
                        symbol = CodeSymbol(
                            name=title,
                            type=f'header_h{level}',
                            file_path=file_path,
                            line_number=line_num,
                            content=line.strip(),
                            language=language
                        )
                        symbols.append(symbol)
            
            elif language in ['json', 'yaml']:
                # Extract top-level keys
                if language == 'json':
                    try:
                        data = json.loads(content)
                        if isinstance(data, dict):
                            for key in data.keys():
                                symbol = CodeSymbol(
                                    name=str(key),
                                    type='key',
                                    file_path=file_path,
                                    line_number=1,
                                    content=f'"{key}": ...',
                                    language=language
                                )
                                symbols.append(symbol)
                    except json.JSONDecodeError:
                        pass
        
        except Exception as e:
            logger.error(f"Error parsing {language} file {file_path}: {e}")
        
        return symbols
    
    def _store_symbols(self, symbols: List[CodeSymbol], file_path: str):
        """Store symbols in database"""
        try:
            file_hash = self._get_file_hash(file_path)
            file_mtime = os.path.getmtime(file_path)
            
            with sqlite3.connect(self.db_path) as conn:
                # Remove old symbols for this file
                conn.execute('DELETE FROM symbols WHERE file_path = ?', (file_path,))
                
                # Insert new symbols
                for symbol in symbols:
                    conn.execute('''
                        INSERT OR REPLACE INTO symbols 
                        (name, type, file_path, line_number, content, docstring, parent, language, file_hash)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        symbol.name, symbol.type, symbol.file_path, symbol.line_number,
                        symbol.content, symbol.docstring, symbol.parent, symbol.language, file_hash
                    ))
                
                # Update file metadata
                conn.execute('''
                    INSERT OR REPLACE INTO file_metadata 
                    (file_path, file_hash, last_modified)
                    VALUES (?, ?, ?)
                ''', (file_path, file_hash, file_mtime))
        
        except Exception as e:
            logger.error(f"Error storing symbols for {file_path}: {e}")
    
    def index_workspace(self, max_files: int = 10000) -> Dict[str, Any]:
        """Index entire workspace"""
        start_time = time.time()
        indexed_files = 0
        total_symbols = 0
        errors = []
        
        try:
            for root, dirs, files in os.walk(self.workspace_root):
                # Skip common ignore directories
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in {
                    'node_modules', '__pycache__', 'venv', 'env', 'build', 'dist',
                    'target', 'bin', 'obj', '.git', '.svn', '.hg'
                }]
                
                for file in files:
                    if indexed_files >= max_files:
                        break
                    
                    file_path = os.path.join(root, file)
                    ext = Path(file).suffix.lower()
                    
                    if ext in self.supported_extensions:
                        try:
                            symbols = self.index_file(file_path)
                            if symbols:
                                indexed_files += 1
                                total_symbols += len(symbols)
                        except Exception as e:
                            errors.append(f"{file_path}: {str(e)}")
                
                if indexed_files >= max_files:
                    break
        
        except Exception as e:
            errors.append(f"Workspace indexing error: {str(e)}")
        
        duration = time.time() - start_time
        
        return {
            'indexed_files': indexed_files,
            'total_symbols': total_symbols,
            'duration_seconds': duration,
            'errors': errors[:10]  # Limit error list
        }
    
    def search(self, query: str, max_results: int = 50) -> List[SearchResult]:
        """Search for symbols matching query"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Multi-part search strategy
                results = []
                
                # 1. Exact name match
                cursor = conn.execute('''
                    SELECT name, type, file_path, line_number, content, docstring, parent, language
                    FROM symbols 
                    WHERE name = ? 
                    ORDER BY type, name
                    LIMIT ?
                ''', (query, max_results // 4))
                
                for row in cursor.fetchall():
                    symbol = CodeSymbol(*row)
                    results.append(SearchResult(
                        symbol=symbol,
                        relevance_score=1.0,
                        context=f"Exact match for '{query}'",
                        snippet=symbol.content[:200]
                    ))
                
                # 2. Name contains query
                if len(results) < max_results:
                    cursor = conn.execute('''
                        SELECT name, type, file_path, line_number, content, docstring, parent, language
                        FROM symbols 
                        WHERE name LIKE ? AND name != ?
                        ORDER BY LENGTH(name), type, name
                        LIMIT ?
                    ''', (f'%{query}%', query, max_results // 4))
                    
                    for row in cursor.fetchall():
                        symbol = CodeSymbol(*row)
                        results.append(SearchResult(
                            symbol=symbol,
                            relevance_score=0.8,
                            context=f"Name contains '{query}'",
                            snippet=symbol.content[:200]
                        ))
                
                # 3. Content contains query
                if len(results) < max_results:
                    cursor = conn.execute('''
                        SELECT name, type, file_path, line_number, content, docstring, parent, language
                        FROM symbols 
                        WHERE (content LIKE ? OR docstring LIKE ?) 
                        AND name NOT LIKE ?
                        ORDER BY type, name
                        LIMIT ?
                    ''', (f'%{query}%', f'%{query}%', f'%{query}%', max_results // 4))
                    
                    for row in cursor.fetchall():
                        symbol = CodeSymbol(*row)
                        results.append(SearchResult(
                            symbol=symbol,
                            relevance_score=0.6,
                            context=f"Content contains '{query}'",
                            snippet=symbol.content[:200]
                        ))
                
                # 4. Fuzzy search for similar names
                if len(results) < max_results:
                    # Simple fuzzy matching
                    query_words = query.lower().split()
                    cursor = conn.execute('''
                        SELECT name, type, file_path, line_number, content, docstring, parent, language
                        FROM symbols 
                        ORDER BY name
                    ''')
                    
                    for row in cursor.fetchall():
                        symbol = CodeSymbol(*row)
                        name_lower = symbol.name.lower()
                        
                        # Check if any query word is in the symbol name
                        if any(word in name_lower for word in query_words):
                            if not any(r.symbol.name == symbol.name and r.symbol.file_path == symbol.file_path 
                                     for r in results):
                                results.append(SearchResult(
                                    symbol=symbol,
                                    relevance_score=0.4,
                                    context=f"Similar to '{query}'",
                                    snippet=symbol.content[:200]
                                ))
                                
                                if len(results) >= max_results:
                                    break
                
                return results[:max_results]
        
        except Exception as e:
            logger.error(f"Error searching for '{query}': {e}")
            return []

class CodebaseRetrievalTool(BaseTool):
    """
    Advanced semantic code search tool - exact replica of Augment Agent's codebase-retrieval
    """
    
    def __init__(self):
        super().__init__()
        self.indexers: Dict[str, CodebaseIndexer] = {}
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.CODE_INTELLIGENCE
    
    def _get_description(self) -> str:
        return """This tool is Augment's context engine, the world's best codebase context engine. It:
1. Takes in a natural language description of the code you are looking for;
2. Uses a proprietary retrieval/embedding model suite that produces the highest-quality recall of relevant code snippets from across the codebase;
3. Maintains a real-time index of the codebase, so the results are always up-to-date and reflects the current state of the codebase;
4. Can retrieve across different programming languages;
5. Only reflects the current state of the codebase on the disk, and has no information on version control or code history."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "information_request": {
                    "description": "A description of the information you need.",
                    "type": "string"
                }
            },
            "required": ["information_request"]
        }
    
    def _get_indexer(self, workspace_root: str) -> CodebaseIndexer:
        """Get or create indexer for workspace"""
        if workspace_root not in self.indexers:
            index_path = os.path.join(workspace_root, '.augment_indexes', 'codebase')
            self.indexers[workspace_root] = CodebaseIndexer(workspace_root, index_path)
        return self.indexers[workspace_root]
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute codebase retrieval"""
        try:
            information_request = kwargs.get('information_request', '')
            
            if not information_request:
                return ToolResult(
                    success=False,
                    error="information_request parameter is required"
                )
            
            # Get workspace root from context
            workspace_root = kwargs.get('_context', {}).get('workspace_root', os.getcwd())
            
            # Get indexer
            indexer = self._get_indexer(workspace_root)
            
            # Index workspace if needed (incremental)
            index_stats = indexer.index_workspace()
            
            # Extract search terms from natural language request
            search_terms = self._extract_search_terms(information_request)
            
            # Perform searches
            all_results = []
            for term in search_terms:
                results = indexer.search(term, max_results=20)
                all_results.extend(results)
            
            # Deduplicate and rank results
            unique_results = self._deduplicate_results(all_results)
            ranked_results = self._rank_results(unique_results, information_request)
            
            # Format results
            formatted_results = self._format_results(ranked_results[:10], information_request)
            
            return ToolResult(
                success=True,
                data={
                    'information_request': information_request,
                    'results_found': len(ranked_results),
                    'results_displayed': len(formatted_results),
                    'search_terms': search_terms,
                    'index_stats': index_stats,
                    'results': formatted_results
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error in codebase retrieval: {str(e)}"
            )

    def _extract_search_terms(self, information_request: str) -> List[str]:
        """Extract search terms from natural language request"""
        # Simple keyword extraction
        import re

        # Remove common words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'between', 'among', 'is', 'are', 'was', 'were', 'be', 'been',
            'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'all', 'any', 'some', 'no', 'not',
            'only', 'own', 'same', 'so', 'than', 'too', 'very', 'just', 'now', 'here',
            'there', 'when', 'where', 'why', 'how', 'what', 'which', 'who', 'whom', 'whose',
            'this', 'that', 'these', 'those', 'i', 'me', 'my', 'myself', 'we', 'our', 'ours',
            'ourselves', 'you', 'your', 'yours', 'yourself', 'yourselves', 'he', 'him', 'his',
            'himself', 'she', 'her', 'hers', 'herself', 'it', 'its', 'itself', 'they', 'them',
            'their', 'theirs', 'themselves'
        }

        # Extract words and filter
        words = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', information_request.lower())
        keywords = [word for word in words if word not in stop_words and len(word) > 2]

        # Add the full request as a search term
        search_terms = [information_request]
        search_terms.extend(keywords[:5])  # Limit to top 5 keywords

        return search_terms

    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """Remove duplicate results"""
        seen = set()
        unique_results = []

        for result in results:
            key = (result.symbol.name, result.symbol.file_path, result.symbol.line_number)
            if key not in seen:
                seen.add(key)
                unique_results.append(result)

        return unique_results

    def _rank_results(self, results: List[SearchResult], query: str) -> List[SearchResult]:
        """Rank results by relevance"""
        query_lower = query.lower()

        def calculate_score(result: SearchResult) -> float:
            score = result.relevance_score

            # Boost score based on symbol type
            type_boost = {
                'class': 1.2,
                'function': 1.1,
                'method': 1.1,
                'variable': 0.9,
                'import': 0.8
            }
            score *= type_boost.get(result.symbol.type, 1.0)

            # Boost if query terms appear in name
            name_lower = result.symbol.name.lower()
            if query_lower in name_lower:
                score *= 1.5

            # Boost if query terms appear in docstring
            if result.symbol.docstring and query_lower in result.symbol.docstring.lower():
                score *= 1.3

            # Boost recent files (if we had modification time)
            # For now, just use a base score

            return score

        # Sort by calculated score
        results.sort(key=calculate_score, reverse=True)
        return results

    def _format_results(self, results: List[SearchResult], query: str) -> List[Dict[str, Any]]:
        """Format results for output"""
        formatted = []

        for result in results:
            symbol = result.symbol

            # Create relative path
            rel_path = os.path.relpath(symbol.file_path)

            # Format symbol info
            symbol_info = {
                'name': symbol.name,
                'type': symbol.type,
                'file_path': rel_path,
                'line_number': symbol.line_number,
                'language': symbol.language,
                'relevance_score': result.relevance_score,
                'context': result.context,
                'snippet': result.snippet
            }

            if symbol.parent:
                symbol_info['parent'] = symbol.parent

            if symbol.docstring:
                symbol_info['docstring'] = symbol.docstring[:200]

            formatted.append(symbol_info)

        return formatted
