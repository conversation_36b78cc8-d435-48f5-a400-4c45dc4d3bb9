# Augment Agent Replica

A complete replica of Augment Agent with all 25+ tools and capabilities, powered by Mistral AI.

## 🚀 Features

### Complete Tool Set (25+ Tools)
- **File Management**: view, str-replace-editor, save-file, remove-files
- **Code Intelligence**: codebase-retrieval, diagnostics
- **Process Management**: launch-process, read/write/kill-process, list-processes, read-terminal
- **Web Integration**: web-search, web-fetch, open-browser
- **Task Management**: view_tasklist, add_tasks, update_tasks, reorganize_tasklist
- **Memory System**: remember, render-mermaid
- **Content Analysis**: view-range-untruncated, search-untruncated

### AI Capabilities
- **Mistral AI Integration**: Powered by Mistral's latest models
- **Semantic Code Search**: Advanced codebase understanding
- **Natural Language Processing**: Human-like interaction
- **Context Awareness**: Maintains conversation context and memory

### Workflows
- **Information Gathering**: Systematic codebase analysis
- **Planning & Task Management**: Structured project organization
- **Implementation**: Precise code editing and generation
- **Testing & Validation**: Comprehensive testing workflows
- **Progress Tracking**: Real-time task state management

## 🏗️ Architecture

```
augment-agent-replica/
├── src/
│   ├── core/
│   │   ├── agent.py              # Main agent logic
│   │   ├── mistral_client.py     # Mistral AI integration
│   │   └── tool_executor.py      # Tool execution framework
│   ├── tools/
│   │   ├── file_management/      # File operation tools
│   │   ├── code_intelligence/    # Code analysis tools
│   │   ├── process_management/   # Process execution tools
│   │   ├── web_integration/      # Web-related tools
│   │   ├── task_management/      # Task organization tools
│   │   ├── memory/              # Memory and documentation
│   │   └── content_analysis/    # Advanced content tools
│   ├── api/
│   │   ├── routes.py            # API endpoints
│   │   └── websocket.py         # Real-time communication
│   ├── web/
│   │   ├── static/              # Frontend assets
│   │   ├── templates/           # HTML templates
│   │   └── app.js               # Frontend logic
│   └── utils/
│       ├── config.py            # Configuration management
│       ├── logger.py            # Logging system
│       └── helpers.py           # Utility functions
├── requirements.txt             # Python dependencies
├── package.json                 # Node.js dependencies
├── docker-compose.yml           # Container orchestration
└── README.md                    # This file
```

## 🛠️ Installation

### Prerequisites
- Python 3.9+
- Node.js 16+
- Docker (optional)
- Mistral AI API key

### Quick Start

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd augment-agent-replica
   pip install -r requirements.txt
   npm install
   ```

2. **Configuration**
   ```bash
   cp .env.example .env
   # Add your Mistral AI API key and other configurations
   ```

3. **Run the Agent**
   ```bash
   # Option 1: Use the dependency installer (recommended)
   python install_dependencies.py

   # Option 2: Manual installation
   python src/main.py --interactive

   # Option 3: Web interface
   python src/main.py --server
   ```

4. **Access Web Interface**
   ```
   http://localhost:8000
   ```

### Troubleshooting

**Import Error: No module named 'mistralai.models.chat_completion'**
```bash
# Install the correct Mistral AI version
pip install mistralai>=1.0.0

# Or use the dependency installer
python install_dependencies.py
```

**Missing Dependencies**
```bash
# Install all dependencies at once
python install_dependencies.py

# Or manually install core dependencies
pip install fastapi uvicorn requests beautifulsoup4 python-dotenv pyyaml psutil
```

**API Key Issues**
1. Get your Mistral AI API key from [console.mistral.ai](https://console.mistral.ai/)
2. Add it to your `.env` file:
   ```
   MISTRAL_API_KEY=your_actual_api_key_here
   ```
3. The agent will run in mock mode without an API key for testing

## 🔧 Configuration

### Environment Variables
```env
MISTRAL_API_KEY=your_mistral_api_key
GOOGLE_SEARCH_API_KEY=your_google_api_key
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id
DATABASE_URL=sqlite:///agent.db
LOG_LEVEL=INFO
```

### Tool Configuration
Each tool can be configured in `src/utils/config.py`:
- File operation limits
- Process timeout settings
- Web search parameters
- Memory retention policies

## 📚 Usage

### Command Line Interface
```bash
# Start interactive session
python src/main.py --interactive

# Execute single command
python src/main.py --command "view src/main.py"

# Run with specific workspace
python src/main.py --workspace /path/to/project
```

### Web Interface
Access the web interface at `http://localhost:8000` for:
- Interactive chat with the agent
- File browser and editor
- Task management dashboard
- Process monitoring
- Memory and context viewer

### API Endpoints
```bash
# Chat with agent
POST /api/chat
{
  "message": "Help me implement authentication",
  "context": {...}
}

# Execute tool directly
POST /api/tools/execute
{
  "tool": "view",
  "parameters": {"path": "src/main.py", "type": "file"}
}

# Task management
GET /api/tasks
POST /api/tasks
PUT /api/tasks/{task_id}
```

## 🧠 How It Works

### Agent Decision Making
The agent replicates the exact decision-making process:
1. **Information Gathering**: Uses codebase-retrieval before any action
2. **Planning**: Creates structured task breakdowns for complex work
3. **Conservative Editing**: Respects existing codebase patterns
4. **Batch Operations**: Efficient task and process management
5. **Testing Focus**: Always suggests testing after changes

### Tool Integration
Each tool is implemented as a separate module with:
- Standardized interface
- Error handling and recovery
- Progress tracking
- Context awareness

### Memory System
- **Short-term**: Conversation context and active tasks
- **Long-term**: User preferences and project patterns
- **Semantic**: Code understanding and relationships

## 🔍 Examples

### Basic File Operations
```python
# View file with regex search
agent.execute_tool("view", {
    "path": "src/main.py",
    "type": "file",
    "search_query_regex": "class.*Controller"
})

# Edit file precisely
agent.execute_tool("str-replace-editor", {
    "command": "str_replace",
    "path": "src/main.py",
    "old_str": "def old_function():",
    "new_str": "def new_function():",
    "old_str_start_line_number": 45,
    "old_str_end_line_number": 45
})
```

### Code Intelligence
```python
# Semantic code search
agent.execute_tool("codebase-retrieval", {
    "information_request": "Find all authentication-related classes and methods"
})

# Get diagnostics
agent.execute_tool("diagnostics", {
    "paths": ["src/auth.py", "src/models.py"]
})
```

### Task Management
```python
# Create structured tasks
agent.execute_tool("add_tasks", {
    "tasks": [
        {
            "name": "Implement user authentication",
            "description": "Create login/logout with JWT tokens"
        },
        {
            "name": "Add password reset",
            "description": "Email-based password reset flow"
        }
    ]
})
```

## 🧪 Testing

```bash
# Run all tests
python -m pytest tests/

# Test specific tool
python -m pytest tests/tools/test_file_management.py

# Integration tests
python -m pytest tests/integration/

# Performance tests
python -m pytest tests/performance/
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Implement changes with tests
4. Submit pull request

## 📄 License

MIT License - see LICENSE file for details.
