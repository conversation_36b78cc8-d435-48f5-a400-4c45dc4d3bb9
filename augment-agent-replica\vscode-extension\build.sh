#!/bin/bash

# Build script for Augment Agent Replica VSCode Extension

echo "🔧 Building Augment Agent Replica VSCode Extension..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Compile TypeScript
echo "🔨 Compiling TypeScript..."
npm run compile

if [ $? -ne 0 ]; then
    echo "❌ TypeScript compilation failed"
    exit 1
fi

# Install vsce if not present
if ! command -v vsce &> /dev/null; then
    echo "📦 Installing vsce (Visual Studio Code Extension manager)..."
    npm install -g vsce
fi

# Package the extension
echo "📦 Packaging extension..."
vsce package

if [ $? -ne 0 ]; then
    echo "❌ Extension packaging failed"
    exit 1
fi

echo "✅ Extension built successfully!"
echo "📁 Extension package: augment-agent-replica-1.0.0.vsix"
echo ""
echo "🚀 To install the extension:"
echo "   code --install-extension augment-agent-replica-1.0.0.vsix"
echo ""
echo "🔧 To develop the extension:"
echo "   1. Open this folder in VSCode"
echo "   2. Press F5 to launch Extension Development Host"
echo "   3. Test your extension in the new window"
