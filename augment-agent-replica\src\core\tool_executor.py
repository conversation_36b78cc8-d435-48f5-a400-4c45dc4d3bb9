"""
Tool Execution Framework for Augment Agent Replica
Handles registration, validation, and execution of all tools
"""

import asyncio
import json
import logging
import traceback
from typing import Dict, List, Any, Optional, Callable, Type
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
import time

logger = logging.getLogger(__name__)

class ToolCategory(Enum):
    """Tool categories matching original Augment Agent"""
    FILE_MANAGEMENT = "file_management"
    CODE_INTELLIGENCE = "code_intelligence"
    PROCESS_MANAGEMENT = "process_management"
    WEB_INTEGRATION = "web_integration"
    TASK_MANAGEMENT = "task_management"
    MEMORY = "memory"
    CONTENT_ANALYSIS = "content_analysis"

@dataclass
class ToolResult:
    """Standardized tool execution result"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    tool_name: str = ""
    metadata: Dict[str, Any] = None

class BaseTool(ABC):
    """Base class for all tools - replicates original Augment Agent tool interface"""
    
    def __init__(self):
        self.name = self.__class__.__name__.lower().replace('tool', '')
        self.category = self._get_category()
        self.description = self._get_description()
        self.parameters = self._get_parameters()
    
    @abstractmethod
    def _get_category(self) -> ToolCategory:
        """Return the tool category"""
        pass
    
    @abstractmethod
    def _get_description(self) -> str:
        """Return tool description"""
        pass
    
    @abstractmethod
    def _get_parameters(self) -> Dict[str, Any]:
        """Return tool parameters schema"""
        pass
    
    @abstractmethod
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the tool with given parameters"""
        pass
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """Validate parameters against schema"""
        required_params = self.parameters.get('required', [])
        
        # Check required parameters
        for param in required_params:
            if param not in parameters:
                raise ValueError(f"Missing required parameter: {param}")
        
        # Check parameter types
        properties = self.parameters.get('properties', {})
        for param, value in parameters.items():
            if param in properties:
                expected_type = properties[param].get('type')
                if expected_type and not self._validate_type(value, expected_type):
                    raise ValueError(f"Invalid type for parameter {param}: expected {expected_type}")
        
        return True
    
    def _validate_type(self, value: Any, expected_type: str) -> bool:
        """Validate parameter type"""
        type_mapping = {
            'string': str,
            'integer': int,
            'number': (int, float),
            'boolean': bool,
            'array': list,
            'object': dict
        }
        
        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type:
            return isinstance(value, expected_python_type)
        
        return True

class ToolExecutor:
    """
    Central tool execution system that replicates Augment Agent's tool handling
    """
    
    def __init__(self):
        self.tools: Dict[str, BaseTool] = {}
        self.execution_history: List[Dict[str, Any]] = []
        self.max_history = 1000
        
        # Performance tracking
        self.execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'average_execution_time': 0.0
        }
    
    def register_tool(self, tool: BaseTool) -> None:
        """Register a tool for execution"""
        self.tools[tool.name] = tool
        logger.info(f"Registered tool: {tool.name} ({tool.category.value})")
    
    def register_tools_from_modules(self, modules: List[Any]) -> None:
        """Register all tools from given modules"""
        for module in modules:
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and 
                    issubclass(attr, BaseTool) and 
                    attr != BaseTool):
                    tool_instance = attr()
                    self.register_tool(tool_instance)
    
    def get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """Get tool by name, handling both hyphenated and non-hyphenated names"""
        # Try exact match first
        if tool_name in self.tools:
            return self.tools[tool_name]

        # Try with hyphens replaced by underscores
        normalized_name = tool_name.replace('-', '')
        if normalized_name in self.tools:
            return self.tools[normalized_name]

        # Try finding by matching the actual tool name
        for registered_name, tool in self.tools.items():
            if (registered_name.replace('-', '').replace('_', '') ==
                tool_name.replace('-', '').replace('_', '')):
                return tool

        return None
    
    def list_tools(self, category: Optional[ToolCategory] = None) -> List[Dict[str, Any]]:
        """List all available tools, optionally filtered by category"""
        tools_list = []
        
        for tool_name, tool in self.tools.items():
            if category is None or tool.category == category:
                tools_list.append({
                    'name': tool_name,
                    'category': tool.category.value,
                    'description': tool.description,
                    'parameters': tool.parameters
                })
        
        return tools_list
    
    async def execute_tool(
        self, 
        tool_name: str, 
        parameters: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> ToolResult:
        """
        Execute a tool with given parameters
        Replicates the exact execution pattern of original Augment Agent
        """
        start_time = time.time()
        
        try:
            # Get tool
            tool = self.get_tool(tool_name)
            if not tool:
                return ToolResult(
                    success=False,
                    error=f"Tool '{tool_name}' not found",
                    tool_name=tool_name,
                    execution_time=time.time() - start_time
                )
            
            # Validate parameters
            tool.validate_parameters(parameters)
            
            # Add context if provided
            if context:
                parameters['_context'] = context
            
            # Execute tool
            logger.info(f"Executing tool: {tool_name} with parameters: {parameters}")
            result = await tool.execute(**parameters)
            
            # Update execution time
            result.execution_time = time.time() - start_time
            result.tool_name = tool_name
            
            # Track execution
            self._track_execution(tool_name, parameters, result)
            
            logger.info(f"Tool {tool_name} executed successfully in {result.execution_time:.2f}s")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Error executing tool {tool_name}: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            
            result = ToolResult(
                success=False,
                error=error_msg,
                tool_name=tool_name,
                execution_time=execution_time
            )
            
            self._track_execution(tool_name, parameters, result)
            return result
    
    async def execute_tool_sequence(
        self, 
        tool_sequence: List[Dict[str, Any]],
        context: Optional[Dict[str, Any]] = None
    ) -> List[ToolResult]:
        """
        Execute a sequence of tools, passing results between them
        Useful for complex workflows
        """
        results = []
        accumulated_context = context or {}
        
        for i, tool_call in enumerate(tool_sequence):
            tool_name = tool_call['tool']
            parameters = tool_call['parameters']
            
            # Add previous results to context
            if results:
                accumulated_context['previous_results'] = results
                accumulated_context['previous_result'] = results[-1]
            
            result = await self.execute_tool(tool_name, parameters, accumulated_context)
            results.append(result)
            
            # Stop on first failure if specified
            if not result.success and tool_call.get('stop_on_failure', True):
                logger.warning(f"Tool sequence stopped at step {i+1} due to failure")
                break
        
        return results
    
    def _track_execution(
        self, 
        tool_name: str, 
        parameters: Dict[str, Any], 
        result: ToolResult
    ) -> None:
        """Track tool execution for analytics and debugging"""
        
        execution_record = {
            'timestamp': time.time(),
            'tool_name': tool_name,
            'parameters': parameters,
            'success': result.success,
            'execution_time': result.execution_time,
            'error': result.error
        }
        
        self.execution_history.append(execution_record)
        
        # Maintain history size
        if len(self.execution_history) > self.max_history:
            self.execution_history = self.execution_history[-self.max_history:]
        
        # Update stats
        self.execution_stats['total_executions'] += 1
        if result.success:
            self.execution_stats['successful_executions'] += 1
        else:
            self.execution_stats['failed_executions'] += 1
        
        # Update average execution time
        total_time = sum(record['execution_time'] for record in self.execution_history)
        self.execution_stats['average_execution_time'] = total_time / len(self.execution_history)
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics"""
        return {
            **self.execution_stats,
            'success_rate': (
                self.execution_stats['successful_executions'] / 
                max(self.execution_stats['total_executions'], 1)
            ) * 100,
            'total_tools_registered': len(self.tools),
            'recent_executions': self.execution_history[-10:]  # Last 10 executions
        }
    
    def get_tool_definitions_for_ai(self) -> List[Dict[str, Any]]:
        """
        Get tool definitions formatted for AI model consumption
        Matches the format expected by Mistral AI and other models
        """
        definitions = []
        
        for tool_name, tool in self.tools.items():
            definition = {
                "type": "function",
                "function": {
                    "name": tool_name,
                    "description": tool.description,
                    "parameters": tool.parameters
                }
            }
            definitions.append(definition)
        
        return definitions
    
    async def handle_ai_tool_calls(self, tool_calls: List[Dict[str, Any]]) -> List[ToolResult]:
        """
        Handle tool calls from AI model
        Processes the exact format returned by AI models
        """
        results = []
        
        for tool_call in tool_calls:
            function = tool_call.get('function', {})
            tool_name = function.get('name', '')
            
            # Parse parameters
            try:
                parameters = json.loads(function.get('arguments', '{}'))
            except json.JSONDecodeError:
                parameters = {}
            
            result = await self.execute_tool(tool_name, parameters)
            results.append(result)
        
        return results

# Global tool executor instance
tool_executor = ToolExecutor()
