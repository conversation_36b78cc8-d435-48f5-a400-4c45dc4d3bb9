"""
Reorganize Tasklist Tool - Exact replica of Augment Agent's reorganize_tasklist functionality
Reorganize the task list structure for major restructuring
"""

import re
from typing import Dict, Any, List, Optional
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult
from .task_manager import task_manager

class ReorganizeTasklistTool(BaseTool):
    """
    Reorganize the task list structure for major restructuring
    Exact replica of original Augment Agent reorganize_tasklist tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.TASK_MANAGEMENT
    
    def _get_description(self) -> str:
        return """Reorganize the task list structure for the current conversation. Use this only for major restructuring like reordering tasks, changing hierarchy. For individual task updates, use update_tasks tool."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "markdown": {
                    "description": "The markdown representation of the task list to update. Should be in the format specified by the view_tasklist tool. New tasks should have a UUID of 'NEW_UUID'. Must contain exactly one root task with proper hierarchy using dash indentation.",
                    "type": "string"
                }
            },
            "required": ["markdown"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the reorganize_tasklist tool"""
        try:
            # Get conversation ID from context if available
            conversation_id = kwargs.get('_context', {}).get('conversation_id', 'default')
            task_manager.set_conversation_id(conversation_id)
            
            markdown = kwargs.get('markdown', '')
            
            if not markdown:
                return ToolResult(
                    success=False,
                    error="markdown parameter is required"
                )
            
            # Parse the markdown into task hierarchy
            try:
                task_hierarchy = self._parse_markdown_to_hierarchy(markdown)
            except Exception as e:
                return ToolResult(
                    success=False,
                    error=f"Error parsing markdown: {str(e)}"
                )
            
            # Validate the hierarchy
            validation_error = self._validate_hierarchy(task_hierarchy)
            if validation_error:
                return ToolResult(
                    success=False,
                    error=validation_error
                )
            
            # Reorganize the tasks
            success = task_manager.reorganize_tasks(task_hierarchy)
            
            if not success:
                return ToolResult(
                    success=False,
                    error="Failed to reorganize tasks"
                )
            
            # Get updated task list summary
            all_tasks = task_manager.get_tasks()
            summary = {
                'total_tasks': len(all_tasks),
                'not_started': len([t for t in all_tasks if t.state.value == 'NOT_STARTED']),
                'in_progress': len([t for t in all_tasks if t.state.value == 'IN_PROGRESS']),
                'complete': len([t for t in all_tasks if t.state.value == 'COMPLETE']),
                'cancelled': len([t for t in all_tasks if t.state.value == 'CANCELLED'])
            }
            
            # Get new markdown representation
            new_markdown = task_manager.get_task_markdown()
            
            return ToolResult(
                success=True,
                data={
                    'conversation_id': conversation_id,
                    'summary': summary,
                    'new_markdown': new_markdown,
                    'message': f"Successfully reorganized task list with {len(all_tasks)} tasks"
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error reorganizing task list: {str(e)}"
            )
    
    def _parse_markdown_to_hierarchy(self, markdown: str) -> List[Dict[str, Any]]:
        """Parse markdown task list into hierarchical structure"""
        lines = markdown.strip().split('\n')
        hierarchy = []
        stack = []  # Stack to track parent tasks at each level
        
        for line in lines:
            line = line.rstrip()
            if not line:
                continue
            
            # Parse the line
            parsed = self._parse_task_line(line)
            if not parsed:
                continue
            
            level, state, task_id, name, description = parsed
            
            # Create task data
            task_data = {
                'task_id': task_id,
                'name': name,
                'description': description,
                'state': state
            }
            
            # Adjust stack to current level
            while len(stack) > level:
                stack.pop()
            
            # Add to appropriate parent
            if level == 0:
                # Root level task
                hierarchy.append(task_data)
                stack = [task_data]
            else:
                # Subtask
                if not stack:
                    raise ValueError(f"Invalid hierarchy: subtask without parent at line: {line}")
                
                parent = stack[-1]
                if 'subtasks' not in parent:
                    parent['subtasks'] = []
                parent['subtasks'].append(task_data)
                
                # Update stack
                if len(stack) == level:
                    stack[-1] = task_data
                else:
                    stack.append(task_data)
        
        return hierarchy
    
    def _parse_task_line(self, line: str) -> Optional[tuple]:
        """Parse a single task line"""
        # Pattern: {dashes}[state] UUID:uuid NAME:name DESCRIPTION:description
        pattern = r'^(-*)\[(.)\]\s+UUID:([^\s]+)\s+NAME:([^:]+?)\s+DESCRIPTION:(.+)$'
        match = re.match(pattern, line)
        
        if not match:
            return None
        
        dashes, state_char, task_id, name, description = match.groups()
        
        # Determine level from dashes
        level = len(dashes)
        
        # Map state character to state
        state_map = {
            ' ': 'NOT_STARTED',
            '/': 'IN_PROGRESS',
            'x': 'COMPLETE',
            '-': 'CANCELLED'
        }
        
        state = state_map.get(state_char, 'NOT_STARTED')
        
        return level, state, task_id.strip(), name.strip(), description.strip()
    
    def _validate_hierarchy(self, hierarchy: List[Dict[str, Any]]) -> Optional[str]:
        """Validate the task hierarchy"""
        if not hierarchy:
            return "Task hierarchy cannot be empty"
        
        # Check that there's exactly one root task (as per requirement)
        if len(hierarchy) != 1:
            return "Must contain exactly one root task with proper hierarchy using dash indentation"
        
        # Validate all tasks recursively
        def validate_task(task_data: Dict[str, Any], path: str = "") -> Optional[str]:
            # Check required fields
            required_fields = ['task_id', 'name', 'description', 'state']
            for field in required_fields:
                if field not in task_data:
                    return f"Missing required field '{field}' in task at {path}"
            
            # Validate state
            valid_states = ['NOT_STARTED', 'IN_PROGRESS', 'COMPLETE', 'CANCELLED']
            if task_data['state'] not in valid_states:
                return f"Invalid state '{task_data['state']}' in task at {path}. Must be one of: {valid_states}"
            
            # Validate task_id
            if not task_data['task_id'] or not isinstance(task_data['task_id'], str):
                return f"Invalid task_id in task at {path}"
            
            # Validate name and description
            if not task_data['name'] or not isinstance(task_data['name'], str):
                return f"Invalid name in task at {path}"
            
            if not task_data['description'] or not isinstance(task_data['description'], str):
                return f"Invalid description in task at {path}"
            
            # Validate subtasks if present
            if 'subtasks' in task_data:
                if not isinstance(task_data['subtasks'], list):
                    return f"Subtasks must be an array in task at {path}"
                
                for i, subtask in enumerate(task_data['subtasks']):
                    error = validate_task(subtask, f"{path}.subtasks[{i}]")
                    if error:
                        return error
            
            return None
        
        for i, task in enumerate(hierarchy):
            error = validate_task(task, f"root[{i}]")
            if error:
                return error
        
        return None
