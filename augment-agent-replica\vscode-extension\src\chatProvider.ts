import * as vscode from 'vscode';
import { AugmentAgentClient } from './agentClient';

export class AugmentAgentChatProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'augmentAgentChat';
    private _view?: vscode.WebviewView;

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private readonly _agentClient: AugmentAgentClient
    ) { }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Handle messages from webview
        webviewView.webview.onDidReceiveMessage(
            async (data) => {
                switch (data.type) {
                    case 'sendMessage':
                        await this._handleUserMessage(data.message);
                        break;
                    case 'executeCommand':
                        await this._handleCommand(data.command);
                        break;
                    case 'openFile':
                        await this._openFile(data.path, data.line);
                        break;
                    case 'applyEdit':
                        await this._applyEdit(data.edit);
                        break;
                }
            },
            undefined,
            []
        );

        // Send initial message
        this._sendMessage({
            type: 'system',
            content: 'Augment Agent Replica is ready! How can I help you today?',
            timestamp: new Date().toISOString()
        });
    }

    public executeCommand(command: string) {
        this._handleUserMessage(command);
    }

    private async _handleUserMessage(message: string) {
        // Add user message to chat
        this._sendMessage({
            type: 'user',
            content: message,
            timestamp: new Date().toISOString()
        });

        // Show typing indicator
        this._sendMessage({
            type: 'typing',
            content: 'Agent is thinking...'
        });

        try {
            // Get workspace context
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
            const activeFile = vscode.window.activeTextEditor?.document.uri.fsPath;

            // Send to agent
            const response = await this._agentClient.sendMessage(message, {
                workspaceRoot,
                activeFile,
                selection: this._getActiveSelection()
            });

            // Remove typing indicator
            this._sendMessage({ type: 'clearTyping' });

            // Handle streaming response
            for await (const chunk of response) {
                if (chunk.type === 'content') {
                    this._sendMessage({
                        type: 'assistant',
                        content: chunk.content,
                        timestamp: new Date().toISOString()
                    });
                } else if (chunk.type === 'tool_result') {
                    this._handleToolResult(chunk);
                }
            }

        } catch (error) {
            this._sendMessage({ type: 'clearTyping' });
            this._sendMessage({
                type: 'error',
                content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                timestamp: new Date().toISOString()
            });
        }
    }

    private async _handleCommand(command: string) {
        // Handle specific commands
        switch (command) {
            case 'clear':
                this._sendMessage({ type: 'clear' });
                break;
            case 'settings':
                vscode.commands.executeCommand('workbench.action.openSettings', 'augmentAgent');
                break;
            case 'help':
                this._showHelp();
                break;
        }
    }

    private async _handleToolResult(toolResult: any) {
        switch (toolResult.tool_name) {
            // File Management Tools
            case 'view':
                this._sendMessage({
                    type: 'file_content',
                    content: toolResult.data.content,
                    file_path: toolResult.data.path,
                    language: toolResult.data.language,
                    size: toolResult.data.size,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'str-replace-editor':
                this._sendMessage({
                    type: 'edit_result',
                    content: 'File edited successfully',
                    changes: toolResult.data.snippets,
                    file_path: toolResult.data.path,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'save-file':
                this._sendMessage({
                    type: 'file_created',
                    content: `File created: ${toolResult.data.relative_path}`,
                    file_path: toolResult.data.absolute_path,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'remove-files':
                this._sendMessage({
                    type: 'files_removed',
                    content: `Removed ${toolResult.data.removed_files.length} files`,
                    removed_files: toolResult.data.removed_files,
                    timestamp: new Date().toISOString()
                });
                break;

            // Code Intelligence Tools
            case 'codebase-retrieval':
                this._sendMessage({
                    type: 'search_results',
                    content: `Found ${toolResult.data.results_found} results`,
                    results: toolResult.data.results,
                    query: toolResult.data.query,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'diagnostics':
                this._sendMessage({
                    type: 'diagnostics',
                    content: `Found ${toolResult.data.summary.total_issues} issues`,
                    diagnostics: toolResult.data.diagnostics,
                    summary: toolResult.data.summary,
                    timestamp: new Date().toISOString()
                });
                break;

            // Process Management Tools
            case 'launch-process':
                this._sendMessage({
                    type: 'process_launched',
                    content: `Process launched with terminal ID: ${toolResult.data.terminal_id}`,
                    terminal_id: toolResult.data.terminal_id,
                    command: toolResult.data.command,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'read-process':
            case 'read-terminal':
                this._sendMessage({
                    type: 'terminal_output',
                    content: toolResult.data.output,
                    terminal_id: toolResult.data.terminal_id,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'list-processes':
                this._sendMessage({
                    type: 'process_list',
                    content: `Found ${toolResult.data.summary.total_processes} processes`,
                    processes: toolResult.data.processes,
                    summary: toolResult.data.summary,
                    timestamp: new Date().toISOString()
                });
                break;

            // Web Integration Tools
            case 'web-search':
                this._sendMessage({
                    type: 'web_search_results',
                    content: `Found ${toolResult.data.num_results_found} search results`,
                    results: toolResult.data.results,
                    query: toolResult.data.query,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'web-fetch':
                this._sendMessage({
                    type: 'web_content',
                    content: `Fetched content from ${toolResult.data.url}`,
                    markdown: toolResult.data.markdown,
                    title: toolResult.data.title,
                    url: toolResult.data.url,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'open-browser':
                this._sendMessage({
                    type: 'browser_opened',
                    content: `Opened browser: ${toolResult.data.url}`,
                    url: toolResult.data.url,
                    timestamp: new Date().toISOString()
                });
                break;

            // Task Management Tools
            case 'view_tasklist':
                this._sendMessage({
                    type: 'task_list',
                    content: `Task list with ${toolResult.data.summary.total_tasks} tasks`,
                    markdown: toolResult.data.markdown,
                    tasks: toolResult.data.tasks,
                    summary: toolResult.data.summary,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'add_tasks':
                this._sendMessage({
                    type: 'tasks_added',
                    content: `Added ${toolResult.data.summary.tasks_created} tasks`,
                    created_tasks: toolResult.data.created_tasks,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'update_tasks':
                this._sendMessage({
                    type: 'tasks_updated',
                    content: `Updated ${toolResult.data.summary.tasks_updated} tasks`,
                    updated_tasks: toolResult.data.updated_tasks,
                    timestamp: new Date().toISOString()
                });
                break;

            // Memory & Documentation Tools
            case 'remember':
                this._sendMessage({
                    type: 'memory_stored',
                    content: `Memory stored with importance ${toolResult.data.importance}`,
                    memory_id: toolResult.data.memory_id,
                    tags: toolResult.data.tags,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'render-mermaid':
                this._sendMessage({
                    type: 'diagram_created',
                    content: `Created ${toolResult.data.diagram_type} diagram`,
                    html_content: toolResult.data.html_content,
                    diagram_type: toolResult.data.diagram_type,
                    timestamp: new Date().toISOString()
                });
                break;

            // Content Analysis Tools
            case 'view-range-untruncated':
                this._sendMessage({
                    type: 'content_range',
                    content: toolResult.data.content,
                    reference_id: toolResult.data.reference_id,
                    range: toolResult.data.actual_range,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'search-untruncated':
                this._sendMessage({
                    type: 'content_search',
                    content: `Found ${toolResult.data.matches_found} matches`,
                    matches: toolResult.data.matches,
                    search_term: toolResult.data.search_term,
                    timestamp: new Date().toISOString()
                });
                break;

            default:
                this._sendMessage({
                    type: 'tool_result',
                    content: JSON.stringify(toolResult.data, null, 2),
                    tool_name: toolResult.tool_name,
                    timestamp: new Date().toISOString()
                });
        }
    }

    private async _openFile(path: string, line?: number) {
        try {
            const uri = vscode.Uri.file(path);
            const document = await vscode.workspace.openTextDocument(uri);
            const editor = await vscode.window.showTextDocument(document);

            if (line && line > 0) {
                const position = new vscode.Position(line - 1, 0);
                editor.selection = new vscode.Selection(position, position);
                editor.revealRange(new vscode.Range(position, position));
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to open file: ${error}`);
        }
    }

    private async _applyEdit(edit: any) {
        try {
            const uri = vscode.Uri.file(edit.file_path);
            const document = await vscode.workspace.openTextDocument(uri);
            const editor = await vscode.window.showTextDocument(document);

            const workspaceEdit = new vscode.WorkspaceEdit();
            const range = new vscode.Range(
                edit.start_line - 1, 0,
                edit.end_line - 1, document.lineAt(edit.end_line - 1).text.length
            );

            workspaceEdit.replace(uri, range, edit.new_content);
            await vscode.workspace.applyEdit(workspaceEdit);

            vscode.window.showInformationMessage('Edit applied successfully');
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to apply edit: ${error}`);
        }
    }

    private _getActiveSelection(): string | undefined {
        const editor = vscode.window.activeTextEditor;
        if (editor && !editor.selection.isEmpty) {
            return editor.document.getText(editor.selection);
        }
        return undefined;
    }

    private _showHelp() {
        this._sendMessage({
            type: 'help',
            content: `
# Augment Agent Replica Help

## Available Commands:
- **View files**: "view README.md", "show me the main.py file"
- **Edit files**: "fix the bug in line 25", "add error handling"
- **Search code**: "find all authentication functions"
- **Get diagnostics**: "check for errors in this file"
- **Manage tasks**: "create a task list", "mark task as complete"
- **Web search**: "search for Python best practices"
- **Process management**: "run tests", "start the server"

## Keyboard Shortcuts:
- **Ctrl+Shift+A**: Open chat panel
- **Ctrl+Shift+E**: Execute command

## Context Menu:
- Right-click on files to view or edit with agent
- Right-click in editor to get diagnostics

## Tips:
- Be specific about what you want to do
- Mention file names and line numbers when relevant
- Ask for explanations of code or errors
- Request code reviews and suggestions
            `,
            timestamp: new Date().toISOString()
        });
    }

    private _sendMessage(message: any) {
        if (this._view) {
            this._view.webview.postMessage(message);
        }
    }

    private _getHtmlForWebview(_webview: vscode.Webview) {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment Agent</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .message {
            padding: 8px 12px;
            border-radius: 8px;
            max-width: 90%;
            word-wrap: break-word;
        }

        .message.user {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            align-self: flex-end;
        }

        .message.assistant {
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            align-self: flex-start;
        }

        .message.system {
            background-color: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
            align-self: center;
            font-style: italic;
        }

        .message.error {
            background-color: var(--vscode-errorForeground);
            color: var(--vscode-editor-background);
            align-self: center;
        }

        .typing {
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            align-self: flex-start;
            font-style: italic;
            opacity: 0.7;
        }

        .input-container {
            padding: 10px;
            border-top: 1px solid var(--vscode-panel-border);
            display: flex;
            gap: 8px;
        }

        .input-box {
            flex: 1;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            padding: 8px;
            font-family: inherit;
            font-size: inherit;
            resize: none;
            min-height: 20px;
            max-height: 100px;
        }

        .send-button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            cursor: pointer;
            font-family: inherit;
        }

        .send-button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .toolbar {
            padding: 8px;
            border-bottom: 1px solid var(--vscode-panel-border);
            display: flex;
            gap: 8px;
            justify-content: space-between;
            align-items: center;
        }

        .toolbar-button {
            background: none;
            border: 1px solid var(--vscode-button-border);
            color: var(--vscode-button-foreground);
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .toolbar-button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .file-link {
            color: var(--vscode-textLink-foreground);
            text-decoration: underline;
            cursor: pointer;
        }

        .file-link:hover {
            color: var(--vscode-textLink-activeForeground);
        }

        .code-block {
            background-color: var(--vscode-textCodeBlock-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 8px;
            font-family: var(--vscode-editor-font-family);
            font-size: var(--vscode-editor-font-size);
            overflow-x: auto;
            margin: 4px 0;
        }

        .search-result {
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 8px;
            margin: 4px 0;
            background-color: var(--vscode-editor-background);
        }

        .diagnostic-item {
            padding: 4px 8px;
            margin: 2px 0;
            border-radius: 4px;
        }

        .diagnostic-error {
            background-color: var(--vscode-errorForeground);
            color: var(--vscode-editor-background);
        }

        .diagnostic-warning {
            background-color: var(--vscode-warningForeground);
            color: var(--vscode-editor-background);
        }

        .diagnostic-info {
            background-color: var(--vscode-infoForeground);
            color: var(--vscode-editor-background);
        }
    </style>
</head>
<body>
    <div class="toolbar">
        <div>
            <button class="toolbar-button" onclick="executeCommand('help')">Help</button>
            <button class="toolbar-button" onclick="executeCommand('settings')">Settings</button>
        </div>
        <div>
            <button class="toolbar-button" onclick="executeCommand('clear')">Clear</button>
        </div>
    </div>

    <div class="chat-container" id="chatContainer">
        <!-- Messages will be added here -->
    </div>

    <div class="input-container">
        <textarea
            class="input-box"
            id="messageInput"
            placeholder="Ask Augment Agent anything..."
            rows="1"
        ></textarea>
        <button class="send-button" id="sendButton" onclick="sendMessage()">Send</button>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            handleMessage(message);
        });

        function handleMessage(message) {
            switch (message.type) {
                case 'clear':
                    chatContainer.innerHTML = '';
                    break;
                case 'clearTyping':
                    removeTypingIndicator();
                    break;
                case 'typing':
                    showTypingIndicator(message.content);
                    break;
                default:
                    addMessage(message);
                    break;
            }
        }

        function addMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = \`message \${message.type}\`;

            let content = message.content;

            // Format different message types
            if (message.type === 'file_content') {
                content = \`<div class="file-link" onclick="openFile('\${message.file_path}')">\${message.file_path}</div><div class="code-block">\${escapeHtml(content)}</div>\`;
            } else if (message.type === 'search_results') {
                content = formatSearchResults(message.results);
            } else if (message.type === 'diagnostics') {
                content = formatDiagnostics(message.diagnostics);
            } else if (message.type === 'task_list') {
                content = formatTaskList(message.tasks, message.summary);
            } else if (message.type === 'process_list') {
                content = formatProcessList(message.processes, message.summary);
            } else if (message.type === 'web_search_results') {
                content = formatWebSearchResults(message.results);
            } else if (message.type === 'terminal_output') {
                content = \`<div class="terminal-output"><pre>\${escapeHtml(content)}</pre></div>\`;
            } else if (message.type === 'diagram_created') {
                content = \`<div class="diagram-container">\${message.html_content}</div>\`;
            } else if (message.type === 'help') {
                content = content.replace(/\\n/g, '<br>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>');
            } else {
                content = escapeHtml(content);
            }

            messageDiv.innerHTML = content;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function formatSearchResults(results) {
            if (!results || results.length === 0) {
                return 'No results found.';
            }

            return results.map(result => \`
                <div class="search-result">
                    <div class="file-link" onclick="openFile('\${result.file_path}', \${result.line_number})">
                        \${result.name} (\${result.type}) - \${result.file_path}:\${result.line_number}
                    </div>
                    <div class="snippet">\${escapeHtml(result.snippet)}</div>
                    <div class="relevance">Relevance: \${(result.relevance_score * 100).toFixed(1)}%</div>
                </div>
            \`).join('');
        }

        function formatTaskList(tasks, summary) {
            if (!tasks || tasks.length === 0) {
                return 'No tasks found.';
            }

            let html = \`<div class="task-summary">
                Total: \${summary.total_tasks},
                In Progress: \${summary.in_progress},
                Complete: \${summary.complete}
            </div>\`;

            html += tasks.map(task => \`
                <div class="task-item task-\${task.state.toLowerCase()}">
                    <div class="task-header">
                        <span class="task-state">[\${task.state === 'COMPLETE' ? 'x' : task.state === 'IN_PROGRESS' ? '/' : ' '}]</span>
                        <span class="task-name">\${escapeHtml(task.name)}</span>
                    </div>
                    <div class="task-description">\${escapeHtml(task.description)}</div>
                </div>
            \`).join('');

            return html;
        }

        function formatProcessList(processes, summary) {
            if (!processes || processes.length === 0) {
                return 'No processes found.';
            }

            let html = \`<div class="process-summary">
                Total: \${summary.total_processes},
                Running: \${summary.running},
                Completed: \${summary.completed}
            </div>\`;

            html += processes.map(proc => \`
                <div class="process-item process-\${proc.state.toLowerCase()}">
                    <div class="process-header">
                        <span class="process-id">Terminal \${proc.terminal_id}</span>
                        <span class="process-state">\${proc.state}</span>
                    </div>
                    <div class="process-command">\${escapeHtml(proc.command)}</div>
                    <div class="process-cwd">\${escapeHtml(proc.cwd)}</div>
                </div>
            \`).join('');

            return html;
        }

        function formatWebSearchResults(results) {
            if (!results || results.length === 0) {
                return 'No web results found.';
            }

            return results.map(result => \`
                <div class="web-result">
                    <div class="web-title">
                        <a href="\${result.url}" target="_blank">\${escapeHtml(result.title)}</a>
                    </div>
                    <div class="web-url">\${escapeHtml(result.url)}</div>
                    <div class="web-snippet">\${escapeHtml(result.snippet)}</div>
                </div>
            \`).join('');
        }

        // Enhanced CSS for all tool results
        const additionalStyles = \`
            .task-item {
                border: 1px solid var(--vscode-panel-border);
                border-radius: 4px;
                padding: 8px;
                margin: 4px 0;
            }

            .task-complete { background-color: var(--vscode-testing-iconPassed); }
            .task-in_progress { background-color: var(--vscode-testing-iconQueued); }
            .task-not_started { background-color: var(--vscode-input-background); }

            .process-item {
                border: 1px solid var(--vscode-panel-border);
                border-radius: 4px;
                padding: 8px;
                margin: 4px 0;
                font-family: var(--vscode-editor-font-family);
            }

            .web-result {
                border: 1px solid var(--vscode-panel-border);
                border-radius: 4px;
                padding: 8px;
                margin: 4px 0;
            }

            .web-title a {
                color: var(--vscode-textLink-foreground);
                text-decoration: none;
                font-weight: bold;
            }

            .terminal-output {
                background-color: var(--vscode-terminal-background);
                color: var(--vscode-terminal-foreground);
                border-radius: 4px;
                padding: 8px;
                margin: 4px 0;
            }

            .diagram-container {
                border: 1px solid var(--vscode-panel-border);
                border-radius: 4px;
                padding: 8px;
                margin: 4px 0;
                background-color: var(--vscode-editor-background);
            }

            .snippet {
                font-family: var(--vscode-editor-font-family);
                font-size: 0.9em;
                color: var(--vscode-descriptionForeground);
            }

            .relevance {
                font-size: 0.8em;
                color: var(--vscode-descriptionForeground);
                font-style: italic;
            }
        \`;

        // Add styles to document
        const styleSheet = document.createElement('style');
        styleSheet.textContent = additionalStyles;
        document.head.appendChild(styleSheet);

        // Focus input on load
        messageInput.focus();
    </script>
</body>
</html>\`;
    }
}

        function formatDiagnostics(diagnostics) {
            if (!diagnostics || diagnostics.length === 0) {
                return 'No issues found.';
            }

            return diagnostics.map(diag => \`
                <div class="diagnostic-item diagnostic-\${diag.severity}">
                    <div class="file-link" onclick="openFile('\${diag.file_path}', \${diag.line_number})">
                        \${diag.file_path}:\${diag.line_number}:\${diag.column}
                    </div>
                    <div>\${escapeHtml(diag.message)}</div>
                </div>
            \`).join('');
        }

        function showTypingIndicator(text) {
            removeTypingIndicator();
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message typing';
            typingDiv.id = 'typingIndicator';
            typingDiv.textContent = text;
            chatContainer.appendChild(typingDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function removeTypingIndicator() {
            const typing = document.getElementById('typingIndicator');
            if (typing) {
                typing.remove();
            }
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (message) {
                vscode.postMessage({
                    type: 'sendMessage',
                    message: message
                });
                messageInput.value = '';
                adjustTextareaHeight();
            }
        }

        function executeCommand(command) {
            vscode.postMessage({
                type: 'executeCommand',
                command: command
            });
        }

        function openFile(path, line) {
            vscode.postMessage({
                type: 'openFile',
                path: path,
                line: line
            });
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function adjustTextareaHeight() {
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 100) + 'px';
        }

        // Event listeners
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        messageInput.addEventListener('input', adjustTextareaHeight);

        // Focus input on load
        messageInput.focus();
    </script>
</body>
</html>`;
    }
