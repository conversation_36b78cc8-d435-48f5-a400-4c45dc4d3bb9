"""
View Tool - Exact replica of Augment Agent's view functionality
Supports file viewing, directory listing, and regex search
"""

import os
import re
import mimetypes
from pathlib import Path
from typing import Dict, Any, List, Optional
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult

class ViewTool(BaseTool):
    """
    Custom tool for viewing files and directories and searching within files with regex query
    Exact replica of original Augment Agent view tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.FILE_MANAGEMENT
    
    def _get_description(self) -> str:
        return """Custom tool for viewing files and directories and searching within files with regex query
* `path` is a file or directory path relative to the workspace root
* For files: displays the result of applying `cat -n` to the file
* For directories: lists files and subdirectories up to 2 levels deep
* If the output is long, it will be truncated and marked with `<response clipped>`

Regex search (for files only):
* Use `search_query_regex` to search for patterns in the file using regular expressions
* Use `case_sensitive` parameter to control case sensitivity (default: false)
* When using regex search, only matching lines and their context will be shown
* Use `context_lines_before` and `context_lines_after` to control how many lines of context to show (default: 5)
* Non-matching sections between matches are replaced with `...`
* If `view_range` is also specified, the search is limited to that range

Use the following regex syntax for `search_query_regex`:

# Regex Syntax Reference

Only the core regex feature common across JavaScript and Rust are supported.

## Supported regex syntax

* **Escaping** - Escape metacharacters with a backslash: `\\.` `\\+` `\\?` `\\*` `\\|` `\\(` `\\)` `\\[`.
* **Dot** `.` - matches any character **except newline** (`\\n`, `\\r`, `\\u2028`, `\\u2029`).
* **Character classes** - `[abc]`, ranges such as `[a-z]`, and negation `[^\u2026]`. Use explicit ASCII ranges; avoid shorthand like `\\d`.
* **Alternation** - `foo|bar` chooses the leftmost successful branch.
* **Quantifiers** - `*`, `+`, `?`, `{n}`, `{n,}`, `{n,m}` (greedy). Add `?` after any of these for the lazy version.
* **Anchors** - `^` (start of line), `$` (end of line).
* **Special characters** - Use `\\t` for tab character

---

## Do **Not** Use (Unsupported)

* Newline character `\\n`. Only single line mode is supported.
* Look-ahead / look-behind `(?= \u2026 )`, `(?<= \u2026 )`.
* Back-references `\\1`, `\\k<name>`.
* Groups `(?<name> \u2026 )`, `(?P<name> \u2026 )`.
* Shorthand classes `\\d`, `\\s`, `\\w`, `\\b`, Unicode property escapes `\\p{\u2026}`.
* Flags inside pattern `(?i)`, `(?m)`, etc.
* Recursion, conditionals, atomic groups, possessive quantifiers
* Unicode escapes like these `\\u{1F60A}` or `\\u1F60A`.


Notes for using the tool:
* Strongly prefer to use `search_query_regex` instead of `view_range` when looking for a specific symbol in the file.
* Use the `view_range` parameter to specify a range of lines to view, e.g. [501, 1000] will show lines from 501 to 1000
* Indices are 1-based and inclusive
* Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file
* The `view_range` and `search_query_regex` parameters are only applicable when viewing files, not directories"""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "path": {
                    "description": "Full path to file or directory relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.",
                    "type": "string"
                },
                "type": {
                    "description": "Type of path to view. Allowed options are: 'file', 'directory'.",
                    "enum": ["file", "directory"],
                    "type": "string"
                },
                "search_query_regex": {
                    "description": "Optional parameter for files only. The regex pattern to search for. Only use core regex syntax common to JavaScript and Rust. See the regex syntax guide in the tool description. When specified, only lines matching the pattern (plus context lines) will be shown. Non-matching sections are replaced with '...'.",
                    "type": "string"
                },
                "case_sensitive": {
                    "description": "Whether the regex search should be case-sensitive. Only used when search_query_regex is specified. Default: false (case-insensitive).",
                    "type": "boolean",
                    "default": False
                },
                "context_lines_before": {
                    "description": "Number of lines to show before each regex match. Only used when search_query_regex is specified. Default: 5.",
                    "type": "integer",
                    "default": 5
                },
                "context_lines_after": {
                    "description": "Number of lines to show after each regex match. Only used when search_query_regex is specified. Default: 5.",
                    "type": "integer",
                    "default": 5
                },
                "view_range": {
                    "description": "Optional parameter when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [501, 1000] will show lines from 501 to 1000. Indices are 1-based and inclusive. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.",
                    "type": "array",
                    "items": {"type": "integer"}
                }
            },
            "required": ["path", "type"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the view tool"""
        try:
            path = kwargs.get('path', '')
            view_type = kwargs.get('type', 'file')
            search_query_regex = kwargs.get('search_query_regex')
            case_sensitive = kwargs.get('case_sensitive', False)
            context_lines_before = kwargs.get('context_lines_before', 5)
            context_lines_after = kwargs.get('context_lines_after', 5)
            view_range = kwargs.get('view_range')
            
            # Get workspace root from context or use current directory
            workspace_root = kwargs.get('_context', {}).get('workspace_root', os.getcwd())
            full_path = os.path.join(workspace_root, path)
            
            if view_type == 'directory':
                return await self._view_directory(full_path)
            elif view_type == 'file':
                return await self._view_file(
                    full_path, 
                    search_query_regex, 
                    case_sensitive,
                    context_lines_before,
                    context_lines_after,
                    view_range
                )
            else:
                return ToolResult(
                    success=False,
                    error=f"Invalid type: {view_type}. Must be 'file' or 'directory'"
                )
                
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error in view tool: {str(e)}"
            )
    
    async def _view_directory(self, path: str) -> ToolResult:
        """View directory contents up to 2 levels deep"""
        try:
            if not os.path.exists(path):
                return ToolResult(
                    success=False,
                    error=f"Directory not found: {path}"
                )
            
            if not os.path.isdir(path):
                return ToolResult(
                    success=False,
                    error=f"Path is not a directory: {path}"
                )
            
            result_lines = [f"Directory listing for: {path}\n"]
            
            # List contents up to 2 levels deep
            for root, dirs, files in os.walk(path):
                level = root.replace(path, '').count(os.sep)
                if level >= 2:
                    dirs[:] = []  # Don't go deeper
                    continue
                
                indent = '  ' * level
                result_lines.append(f"{indent}{os.path.basename(root)}/")
                
                subindent = '  ' * (level + 1)
                for file in sorted(files):
                    file_path = os.path.join(root, file)
                    file_size = os.path.getsize(file_path)
                    result_lines.append(f"{subindent}{file} ({file_size} bytes)")
            
            content = '\n'.join(result_lines)
            
            return ToolResult(
                success=True,
                data={
                    'content': content,
                    'type': 'directory',
                    'path': path
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error viewing directory: {str(e)}"
            )
    
    async def _view_file(
        self, 
        path: str, 
        search_query_regex: Optional[str] = None,
        case_sensitive: bool = False,
        context_lines_before: int = 5,
        context_lines_after: int = 5,
        view_range: Optional[List[int]] = None
    ) -> ToolResult:
        """View file contents with optional regex search and range"""
        try:
            if not os.path.exists(path):
                return ToolResult(
                    success=False,
                    error=f"File not found: {path}"
                )
            
            if not os.path.isfile(path):
                return ToolResult(
                    success=False,
                    error=f"Path is not a file: {path}"
                )
            
            # Check if file is binary
            if self._is_binary_file(path):
                return ToolResult(
                    success=False,
                    error=f"Cannot view binary file: {path}"
                )
            
            # Read file content
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
            except UnicodeDecodeError:
                # Try with different encoding
                with open(path, 'r', encoding='latin-1') as f:
                    lines = f.readlines()
            
            # Apply view range if specified
            if view_range:
                start_line = max(1, view_range[0]) - 1  # Convert to 0-based
                end_line = len(lines) if view_range[1] == -1 else min(len(lines), view_range[1])
                lines = lines[start_line:end_line]
                line_offset = start_line
            else:
                line_offset = 0
            
            # Apply regex search if specified
            if search_query_regex:
                return self._search_in_lines(
                    lines, 
                    search_query_regex, 
                    case_sensitive,
                    context_lines_before,
                    context_lines_after,
                    line_offset,
                    path
                )
            else:
                # Return full file with line numbers
                numbered_lines = []
                for i, line in enumerate(lines, start=line_offset + 1):
                    numbered_lines.append(f"{i:6d}\t{line.rstrip()}")
                
                content = '\n'.join(numbered_lines)
                
                # Check if content should be truncated
                max_lines = 1000
                if len(numbered_lines) > max_lines:
                    content = '\n'.join(numbered_lines[:max_lines])
                    content += f"\n\n<response clipped - showing first {max_lines} lines of {len(lines)} total lines>"
                
                return ToolResult(
                    success=True,
                    data={
                        'content': content,
                        'type': 'file',
                        'path': path,
                        'total_lines': len(lines),
                        'displayed_lines': min(len(lines), max_lines)
                    }
                )
                
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error viewing file: {str(e)}"
            )

    def _search_in_lines(
        self,
        lines: List[str],
        search_query_regex: str,
        case_sensitive: bool,
        context_lines_before: int,
        context_lines_after: int,
        line_offset: int,
        path: str
    ) -> ToolResult:
        """Search for regex pattern in lines and return matches with context"""
        try:
            # Compile regex pattern
            flags = 0 if case_sensitive else re.IGNORECASE
            pattern = re.compile(search_query_regex, flags)

            matches = []
            for i, line in enumerate(lines):
                if pattern.search(line):
                    matches.append(i)

            if not matches:
                return ToolResult(
                    success=True,
                    data={
                        'content': f"No matches found for pattern: {search_query_regex}",
                        'type': 'file_search',
                        'path': path,
                        'pattern': search_query_regex,
                        'matches_found': 0
                    }
                )

            # Build result with context
            result_lines = []
            last_shown_line = -1

            for match_line in matches:
                start_context = max(0, match_line - context_lines_before)
                end_context = min(len(lines), match_line + context_lines_after + 1)

                # Add separator if there's a gap
                if start_context > last_shown_line + 1:
                    if result_lines:
                        result_lines.append("...")

                # Add context and match
                for line_idx in range(start_context, end_context):
                    line_num = line_idx + line_offset + 1
                    line_content = lines[line_idx].rstrip()

                    # Highlight the match line
                    if line_idx == match_line:
                        result_lines.append(f"{line_num:6d}*\t{line_content}")
                    else:
                        result_lines.append(f"{line_num:6d}\t{line_content}")

                last_shown_line = end_context - 1

            content = '\n'.join(result_lines)

            return ToolResult(
                success=True,
                data={
                    'content': content,
                    'type': 'file_search',
                    'path': path,
                    'pattern': search_query_regex,
                    'matches_found': len(matches),
                    'case_sensitive': case_sensitive
                }
            )

        except re.error as e:
            return ToolResult(
                success=False,
                error=f"Invalid regex pattern: {str(e)}"
            )
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error in regex search: {str(e)}"
            )

    def _is_binary_file(self, path: str) -> bool:
        """Check if file is binary"""
        try:
            # Check MIME type
            mime_type, _ = mimetypes.guess_type(path)
            if mime_type and not mime_type.startswith('text/'):
                return True

            # Check for null bytes in first 1024 bytes
            with open(path, 'rb') as f:
                chunk = f.read(1024)
                return b'\x00' in chunk

        except Exception:
            return True  # Assume binary if we can't read it
