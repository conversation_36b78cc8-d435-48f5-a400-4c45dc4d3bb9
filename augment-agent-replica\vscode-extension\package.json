{"name": "augment-agent-replica", "displayName": "Augment Agent Replica", "description": "Complete AI coding assistant with chat panel - exact replica of Augment Agent", "version": "1.0.0", "publisher": "augment-code", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Snippets", "Debuggers"], "keywords": ["ai", "assistant", "coding", "chat", "mistral", "augment"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "augmentAgent.openChat", "title": "Open Chat Panel", "category": "Augment Agent", "icon": "$(comment-discussion)"}, {"command": "augmentAgent.executeCommand", "title": "Execute Command", "category": "Augment Agent"}, {"command": "augmentAgent.viewFile", "title": "View File with Agent", "category": "Augment Agent"}, {"command": "augmentAgent.editFile", "title": "Edit File with Agent", "category": "Augment Agent"}, {"command": "augmentAgent.searchCodebase", "title": "Search Codebase", "category": "Augment Agent"}, {"command": "augmentAgent.getDiagnostics", "title": "Get Diagnostics", "category": "Augment Agent"}, {"command": "augmentAgent.manageTasks", "title": "Manage Tasks", "category": "Augment Agent"}], "views": {"explorer": [{"id": "augmentAgentChat", "name": "Augment Agent", "when": "true", "type": "webview"}]}, "viewsContainers": {"activitybar": [{"id": "augmentAgent", "title": "Augment Agent", "icon": "$(robot)"}]}, "configuration": {"title": "Augment Agent", "properties": {"augmentAgent.mistralApiKey": {"type": "string", "default": "", "description": "Mistral AI API Key", "scope": "application"}, "augmentAgent.model": {"type": "string", "default": "mistral-large-latest", "description": "Mistral AI model to use", "enum": ["mistral-large-latest", "mistral-medium-latest", "mistral-small-latest"]}, "augmentAgent.maxTokens": {"type": "number", "default": 4096, "description": "Maximum tokens for responses"}, "augmentAgent.temperature": {"type": "number", "default": 0.1, "minimum": 0, "maximum": 2, "description": "Temperature for AI responses"}, "augmentAgent.autoIndex": {"type": "boolean", "default": true, "description": "Automatically index codebase for semantic search"}, "augmentAgent.showDiagnostics": {"type": "boolean", "default": true, "description": "Show diagnostics in chat panel"}}}, "menus": {"explorer/context": [{"command": "augmentAgent.viewFile", "when": "resourceExtname =~ /\\.(py|js|ts|jsx|tsx|java|cpp|c|h|cs|go|rs|php|rb)$/", "group": "augmentAgent"}, {"command": "augmentAgent.editFile", "when": "resourceExtname =~ /\\.(py|js|ts|jsx|tsx|java|cpp|c|h|cs|go|rs|php|rb)$/", "group": "augmentAgent"}], "editor/context": [{"command": "augmentAgent.editFile", "when": "editorTextFocus", "group": "augmentAgent"}, {"command": "augmentAgent.getDiagnostics", "when": "editorTextFocus", "group": "augmentAgent"}], "commandPalette": [{"command": "augmentAgent.openChat", "when": "true"}, {"command": "augmentAgent.searchCodebase", "when": "true"}, {"command": "augmentAgent.manageTasks", "when": "true"}]}, "keybindings": [{"command": "augmentAgent.openChat", "key": "ctrl+shift+a", "mac": "cmd+shift+a"}, {"command": "augmentAgent.executeCommand", "key": "ctrl+shift+e", "mac": "cmd+shift+e"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.0", "ws": "^8.14.0"}}