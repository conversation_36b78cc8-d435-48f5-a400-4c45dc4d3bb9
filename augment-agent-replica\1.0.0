Requirement already satisfied: mistralai in c:\python310\lib\site-packages (1.8.1)
Requirement already satisfied: eval-type-backport>=0.2.0 in c:\python310\lib\site-packages (from mistralai) (0.2.2)
Requirement already satisfied: httpx>=0.28.1 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from mistralai) (0.28.1)
Requirement already satisfied: pydantic>=2.10.3 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from mistralai) (2.11.5)
Requirement already satisfied: python-dateutil>=2.8.2 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from mistralai) (2.9.0.post0)
Requirement already satisfied: typing-inspection>=0.4.0 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from mistralai) (0.4.1)
Requirement already satisfied: anyio in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from httpx>=0.28.1->mistralai) (4.9.0)
Requirement already satisfied: certifi in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from httpx>=0.28.1->mistralai) (2025.4.26)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from httpx>=0.28.1->mistralai) (1.0.5)
Requirement already satisfied: idna in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from httpx>=0.28.1->mistralai) (3.10)
Requirement already satisfied: h11<0.15,>=0.13 in c:\python310\lib\site-packages (from httpcore==1.*->httpx>=0.28.1->mistralai) (0.14.0)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from pydantic>=2.10.3->mistralai) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from pydantic>=2.10.3->mistralai) (2.33.2)
Requirement already satisfied: typing-extensions>=4.12.2 in c:\python310\lib\site-packages (from pydantic>=2.10.3->mistralai) (4.12.2)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from python-dateutil>=2.8.2->mistralai) (1.17.0)
Requirement already satisfied: exceptiongroup>=1.0.2 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from anyio->httpx>=0.28.1->mistralai) (1.3.0)
Requirement already satisfied: sniffio>=1.1 in c:\users\<USER>\appdata\roaming\python\python310\site-packages (from anyio->httpx>=0.28.1->mistralai) (1.3.1)
