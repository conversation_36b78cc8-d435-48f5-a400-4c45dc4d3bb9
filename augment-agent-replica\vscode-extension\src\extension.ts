import * as vscode from 'vscode';
import { AugmentAgentChatProvider } from './chatProvider';
import { AugmentAgentClient } from './agentClient';
import { MultiAIProviderClient, AI_PROVIDERS } from './aiProviders';

let chatProvider: AugmentAgentChatProvider;
let agentClient: AugmentAgentClient;
let aiProviderClient: MultiAIProviderClient;

export function activate(context: vscode.ExtensionContext) {
    // Initialize AI provider client
    aiProviderClient = new MultiAIProviderClient();

    // Initialize agent client
    agentClient = new AugmentAgentClient();

    // Initialize chat provider
    chatProvider = new AugmentAgentChatProvider(context.extensionUri, agentClient);

    // Register webview provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(
            'augmentAgentChat',
            chatProvider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true
                }
            }
        )
    );

    // Register commands
    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.openChat', () => {
            vscode.commands.executeCommand('workbench.view.extension.augmentAgent');
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.executeCommand', async () => {
            const command = await vscode.window.showInputBox({
                prompt: 'Enter command for Augment Agent',
                placeHolder: 'e.g., view README.md, search for function, create tests'
            });
            
            if (command) {
                chatProvider.executeCommand(command);
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.viewFile', async (uri: vscode.Uri) => {
            if (uri) {
                const relativePath = vscode.workspace.asRelativePath(uri);
                chatProvider.executeCommand(`view ${relativePath}`);
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.editFile', async (uri: vscode.Uri) => {
            if (uri) {
                const relativePath = vscode.workspace.asRelativePath(uri);
                const instruction = await vscode.window.showInputBox({
                    prompt: 'What changes would you like to make?',
                    placeHolder: 'e.g., add error handling, refactor this function, fix bugs'
                });
                
                if (instruction) {
                    chatProvider.executeCommand(`Edit ${relativePath}: ${instruction}`);
                }
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.searchCodebase', async () => {
            const query = await vscode.window.showInputBox({
                prompt: 'Search codebase',
                placeHolder: 'e.g., authentication functions, error handling, API endpoints'
            });
            
            if (query) {
                chatProvider.executeCommand(`Search codebase for: ${query}`);
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.getDiagnostics', async () => {
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor) {
                const relativePath = vscode.workspace.asRelativePath(activeEditor.document.uri);
                chatProvider.executeCommand(`Get diagnostics for ${relativePath}`);
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.manageTasks', async () => {
            const action = await vscode.window.showQuickPick([
                'View task list',
                'Add new task',
                'Update task status',
                'Create project plan'
            ], {
                placeHolder: 'Select task management action'
            });

            if (action) {
                chatProvider.executeCommand(action);
            }
        })
    );

    // ===== ALL REMAINING TOOL COMMANDS =====

    // Process Management Commands
    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.launchProcess', async () => {
            const command = await vscode.window.showInputBox({
                prompt: 'Enter command to execute',
                placeHolder: 'e.g., npm test, python script.py, ls -la'
            });

            if (command) {
                chatProvider.executeCommand(`Run command: ${command}`);
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.listProcesses', () => {
            chatProvider.executeCommand('List all running processes');
        })
    );

    // Web Integration Commands
    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.webSearch', async () => {
            const query = await vscode.window.showInputBox({
                prompt: 'Search the web',
                placeHolder: 'e.g., React hooks tutorial, Python async best practices'
            });

            if (query) {
                chatProvider.executeCommand(`Search web for: ${query}`);
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.webFetch', async () => {
            const url = await vscode.window.showInputBox({
                prompt: 'Enter URL to fetch content',
                placeHolder: 'https://example.com/documentation'
            });

            if (url) {
                chatProvider.executeCommand(`Fetch content from: ${url}`);
            }
        })
    );

    // Memory Commands
    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.remember', async () => {
            const memory = await vscode.window.showInputBox({
                prompt: 'What should I remember?',
                placeHolder: 'e.g., User prefers TypeScript over JavaScript'
            });

            if (memory) {
                chatProvider.executeCommand(`Remember: ${memory}`);
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.createDiagram', async () => {
            const diagramType = await vscode.window.showQuickPick([
                'Flowchart',
                'Sequence Diagram',
                'Class Diagram',
                'Architecture Diagram',
                'Custom Mermaid'
            ], {
                placeHolder: 'Select diagram type'
            });

            if (diagramType) {
                chatProvider.executeCommand(`Create ${diagramType} for current project`);
            }
        })
    );

    // AI Provider Management Commands
    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.switchProvider', async () => {
            const providers = aiProviderClient.getConfiguredProviders();

            if (providers.length === 0) {
                vscode.window.showWarningMessage('No AI providers configured. Please add API keys in settings.');
                return;
            }

            const selected = await vscode.window.showQuickPick(
                providers.map(p => ({
                    label: p.displayName,
                    description: `Model: ${p.defaultModel}`,
                    detail: `Max tokens: ${p.maxTokens}, Streaming: ${p.supportsStreaming}`,
                    provider: p.name
                })),
                {
                    placeHolder: 'Select AI provider'
                }
            );

            if (selected) {
                aiProviderClient.setProvider(selected.provider);
                vscode.window.showInformationMessage(`Switched to ${selected.label}`);
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.configureProviders', () => {
            vscode.commands.executeCommand('workbench.action.openSettings', 'augmentAgent');
        })
    );

    // Advanced Features Commands
    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.analyzeWorkspace', () => {
            chatProvider.executeCommand('Analyze entire workspace structure and provide insights');
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.generateTests', async () => {
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor) {
                const relativePath = vscode.workspace.asRelativePath(activeEditor.document.uri);
                chatProvider.executeCommand(`Generate comprehensive tests for ${relativePath}`);
            } else {
                vscode.window.showWarningMessage('No active file to generate tests for');
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.explainCode', async () => {
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor && !activeEditor.selection.isEmpty) {
                const selectedText = activeEditor.document.getText(activeEditor.selection);
                chatProvider.executeCommand(`Explain this code: ${selectedText}`);
            } else {
                vscode.window.showWarningMessage('Please select code to explain');
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.optimizeCode', async () => {
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor) {
                const relativePath = vscode.workspace.asRelativePath(activeEditor.document.uri);
                chatProvider.executeCommand(`Optimize and improve code in ${relativePath}`);
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('augmentAgent.findBugs', async () => {
            const activeEditor = vscode.window.activeTextEditor;
            if (activeEditor) {
                const relativePath = vscode.workspace.asRelativePath(activeEditor.document.uri);
                chatProvider.executeCommand(`Find and fix bugs in ${relativePath}`);
            }
        })
    );

    // Auto-start agent server if configured
    const config = vscode.workspace.getConfiguration('augmentAgent');
    if (config.get('autoStart', true)) {
        agentClient.startServer();
    }

    // Show welcome message
    vscode.window.showInformationMessage(
        'Augment Agent Replica is ready! Use Ctrl+Shift+A to open chat panel.',
        'Open Chat'
    ).then(selection => {
        if (selection === 'Open Chat') {
            vscode.commands.executeCommand('augmentAgent.openChat');
        }
    });
}

export function deactivate() {
    if (agentClient) {
        agentClient.dispose();
    }
}
