"""
Remember Tool - Exact replica of Augment Agent's remember functionality
Long-term memory storage for important information
"""

from typing import Dict, Any, List, Optional
from ...core.tool_executor import BaseTool, ToolCategory, ToolResult
from .memory_manager import memory_manager

class RememberTool(BaseTool):
    """
    Store information in long-term memory
    Exact replica of original Augment Agent remember tool
    """
    
    def _get_category(self) -> ToolCategory:
        return ToolCategory.MEMORY
    
    def _get_description(self) -> str:
        return """Call this tool when user asks you:
- to remember something
- to create memory/memories

Use this tool only with information that can be useful in the long-term.
Do not use this tool for temporary information."""
    
    def _get_parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "memory": {
                    "description": "The concise (1 sentence) memory to remember.",
                    "type": "string"
                }
            },
            "required": ["memory"]
        }
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the remember tool"""
        try:
            memory_content = kwargs.get('memory', '')
            
            if not memory_content:
                return ToolResult(
                    success=False,
                    error="memory parameter is required"
                )
            
            # Validate memory content
            if len(memory_content.strip()) < 5:
                return ToolResult(
                    success=False,
                    error="Memory content is too short. Please provide meaningful information to remember."
                )
            
            if len(memory_content) > 1000:
                return ToolResult(
                    success=False,
                    error="Memory content is too long. Please keep memories concise (under 1000 characters)."
                )
            
            # Get conversation ID from context if available
            conversation_id = kwargs.get('_context', {}).get('conversation_id', 'default')
            memory_manager.set_conversation_id(conversation_id)
            
            # Extract context from the current interaction
            context = {
                'conversation_id': conversation_id,
                'timestamp': kwargs.get('_context', {}).get('timestamp'),
                'user_request': kwargs.get('_context', {}).get('user_message'),
                'workspace': kwargs.get('_context', {}).get('workspace_root')
            }
            
            # Determine importance based on content
            importance = self._calculate_importance(memory_content)
            
            # Extract tags from content
            tags = self._extract_tags(memory_content)
            
            # Store the memory
            memory_id = memory_manager.store_memory(
                content=memory_content.strip(),
                context=context,
                tags=tags,
                importance=importance
            )
            
            # Get memory statistics
            stats = memory_manager.get_memory_stats(conversation_id)
            
            return ToolResult(
                success=True,
                data={
                    'memory_id': memory_id,
                    'memory_content': memory_content.strip(),
                    'importance': importance,
                    'tags': tags,
                    'conversation_id': conversation_id,
                    'stats': stats,
                    'message': f"Successfully stored memory with ID: {memory_id}"
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Error storing memory: {str(e)}"
            )
    
    def _calculate_importance(self, content: str) -> float:
        """Calculate importance score for memory content"""
        importance = 0.5  # Base importance
        
        content_lower = content.lower()
        
        # High importance keywords
        high_importance_keywords = [
            'important', 'critical', 'remember', 'key', 'essential',
            'password', 'secret', 'api key', 'token', 'credential',
            'preference', 'setting', 'configuration', 'requirement',
            'deadline', 'meeting', 'appointment', 'contact'
        ]
        
        # Medium importance keywords
        medium_importance_keywords = [
            'note', 'todo', 'task', 'project', 'goal', 'plan',
            'idea', 'suggestion', 'recommendation', 'tip',
            'issue', 'problem', 'solution', 'fix'
        ]
        
        # Low importance keywords
        low_importance_keywords = [
            'maybe', 'perhaps', 'might', 'could', 'temporary',
            'test', 'example', 'demo', 'sample'
        ]
        
        # Adjust importance based on keywords
        for keyword in high_importance_keywords:
            if keyword in content_lower:
                importance += 0.3
        
        for keyword in medium_importance_keywords:
            if keyword in content_lower:
                importance += 0.2
        
        for keyword in low_importance_keywords:
            if keyword in content_lower:
                importance -= 0.1
        
        # Adjust based on content length (longer content might be more important)
        if len(content) > 200:
            importance += 0.1
        elif len(content) < 50:
            importance -= 0.1
        
        # Adjust based on specificity (presence of numbers, URLs, specific names)
        if any(char.isdigit() for char in content):
            importance += 0.1
        
        if 'http' in content_lower or 'www.' in content_lower:
            importance += 0.1
        
        if content.count('@') > 0:  # Email addresses
            importance += 0.1
        
        # Clamp to valid range
        return max(0.0, min(1.0, importance))
    
    def _extract_tags(self, content: str) -> List[str]:
        """Extract relevant tags from memory content"""
        tags = []
        content_lower = content.lower()
        
        # Technology tags
        tech_keywords = {
            'python': 'python',
            'javascript': 'javascript',
            'js': 'javascript',
            'typescript': 'typescript',
            'ts': 'typescript',
            'react': 'react',
            'vue': 'vue',
            'angular': 'angular',
            'node': 'nodejs',
            'nodejs': 'nodejs',
            'docker': 'docker',
            'kubernetes': 'kubernetes',
            'k8s': 'kubernetes',
            'aws': 'aws',
            'azure': 'azure',
            'gcp': 'gcp',
            'git': 'git',
            'github': 'github',
            'gitlab': 'gitlab',
            'api': 'api',
            'rest': 'api',
            'graphql': 'graphql',
            'database': 'database',
            'sql': 'database',
            'mongodb': 'database',
            'redis': 'database'
        }
        
        for keyword, tag in tech_keywords.items():
            if keyword in content_lower:
                if tag not in tags:
                    tags.append(tag)
        
        # Project/work tags
        work_keywords = {
            'project': 'project',
            'task': 'task',
            'meeting': 'meeting',
            'deadline': 'deadline',
            'requirement': 'requirement',
            'bug': 'bug',
            'feature': 'feature',
            'issue': 'issue',
            'fix': 'fix',
            'improvement': 'improvement'
        }
        
        for keyword, tag in work_keywords.items():
            if keyword in content_lower:
                if tag not in tags:
                    tags.append(tag)
        
        # Personal tags
        personal_keywords = {
            'preference': 'preference',
            'setting': 'setting',
            'configuration': 'config',
            'config': 'config',
            'password': 'credential',
            'token': 'credential',
            'key': 'credential',
            'secret': 'credential'
        }
        
        for keyword, tag in personal_keywords.items():
            if keyword in content_lower:
                if tag not in tags:
                    tags.append(tag)
        
        # Limit number of tags
        return tags[:5]
