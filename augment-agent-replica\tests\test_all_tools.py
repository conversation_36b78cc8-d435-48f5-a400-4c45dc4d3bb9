"""
Comprehensive test suite for all Augment Agent Replica tools
Tests every single tool to ensure complete functionality
"""

import pytest
import asyncio
import tempfile
import os
import time
from unittest.mock import Mock, patch

from src.core.agent import AugmentAgentReplica
from src.utils.config import Config

@pytest.fixture
def temp_workspace():
    """Create a temporary workspace for testing"""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir

@pytest.fixture
def test_config(temp_workspace):
    """Create test configuration"""
    config = Config()
    config.workspace_root = temp_workspace
    config.mistral_api_key = ""
    return config

@pytest.fixture
def mock_mistral_client():
    """Mock Mistral AI client"""
    with patch('src.core.agent.MistralAIClient') as mock:
        mock_instance = Mock()
        mock_instance.chat_completion.return_value = {
            'content': 'Test response',
            'tool_calls': None
        }
        mock.return_value = mock_instance
        yield mock_instance

@pytest.fixture
def agent(test_config, mock_mistral_client):
    """Create test agent instance"""
    return AugmentAgentReplica(test_config)

class TestFileManagementTools:
    """Test all file management tools"""
    
    @pytest.mark.asyncio
    async def test_view_tool(self, agent, temp_workspace):
        """Test view tool"""
        # Create test file
        test_file = os.path.join(temp_workspace, 'test.txt')
        with open(test_file, 'w') as f:
            f.write('Hello, World!\nLine 2\nLine 3')
        
        result = await agent.execute_tool_directly('view', {
            'path': 'test.txt',
            'type': 'file'
        })
        
        assert result['success']
        assert 'Hello, World!' in result['data']['content']
    
    @pytest.mark.asyncio
    async def test_save_file_tool(self, agent):
        """Test save-file tool"""
        result = await agent.execute_tool_directly('save-file', {
            'instructions_reminder': 'LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.',
            'path': 'new_file.txt',
            'file_content': 'New file content'
        })
        
        assert result['success']
        assert result['data']['relative_path'] == 'new_file.txt'
    
    @pytest.mark.asyncio
    async def test_str_replace_editor_tool(self, agent, temp_workspace):
        """Test str-replace-editor tool"""
        # Create test file
        test_file = os.path.join(temp_workspace, 'edit_test.txt')
        with open(test_file, 'w') as f:
            f.write('Line 1\nLine 2\nLine 3')
        
        result = await agent.execute_tool_directly('str-replace-editor', {
            'command': 'str_replace',
            'path': 'edit_test.txt',
            'instruction_reminder': 'ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH.',
            'old_str_1': 'Line 2',
            'new_str_1': 'Modified Line 2',
            'old_str_start_line_number_1': 2,
            'old_str_end_line_number_1': 2
        })
        
        assert result['success']

class TestCodeIntelligenceTools:
    """Test all code intelligence tools"""
    
    @pytest.mark.asyncio
    async def test_codebase_retrieval_tool(self, agent, temp_workspace):
        """Test codebase-retrieval tool"""
        # Create test Python file
        test_file = os.path.join(temp_workspace, 'test_code.py')
        with open(test_file, 'w') as f:
            f.write('''
def hello_world():
    """Say hello to the world"""
    print("Hello, World!")

class TestClass:
    def test_method(self):
        pass
''')
        
        result = await agent.execute_tool_directly('codebase-retrieval', {
            'information_request': 'find hello world function'
        })
        
        assert result['success']
        assert result['data']['results_found'] >= 0
    
    @pytest.mark.asyncio
    async def test_diagnostics_tool(self, agent, temp_workspace):
        """Test diagnostics tool"""
        # Create test file with syntax error
        test_file = os.path.join(temp_workspace, 'syntax_error.py')
        with open(test_file, 'w') as f:
            f.write('def broken_function(\n    print("Missing closing parenthesis")')
        
        result = await agent.execute_tool_directly('diagnostics', {
            'paths': ['syntax_error.py']
        })
        
        assert result['success']
        assert result['data']['summary']['files_analyzed'] == 1

class TestProcessManagementTools:
    """Test all process management tools"""
    
    @pytest.mark.asyncio
    async def test_launch_process_tool(self, agent, temp_workspace):
        """Test launch-process tool"""
        result = await agent.execute_tool_directly('launch-process', {
            'command': 'echo "Hello from process"',
            'wait': True,
            'max_wait_seconds': 10,
            'cwd': temp_workspace
        })
        
        assert result['success']
        assert 'terminal_id' in result['data']
    
    @pytest.mark.asyncio
    async def test_list_processes_tool(self, agent):
        """Test list-processes tool"""
        result = await agent.execute_tool_directly('list-processes', {})
        
        assert result['success']
        assert 'summary' in result['data']
    
    @pytest.mark.asyncio
    async def test_read_terminal_tool(self, agent):
        """Test read-terminal tool"""
        result = await agent.execute_tool_directly('read-terminal', {
            'only_selected': False
        })
        
        assert result['success']

class TestWebIntegrationTools:
    """Test all web integration tools"""
    
    @pytest.mark.asyncio
    async def test_web_search_tool(self, agent):
        """Test web-search tool"""
        with patch('requests.get') as mock_get:
            mock_response = Mock()
            mock_response.text = '<html><body>Test search results</body></html>'
            mock_response.status_code = 200
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response
            
            result = await agent.execute_tool_directly('web-search', {
                'query': 'test search',
                'num_results': 3
            })
            
            assert result['success']
            assert result['data']['query'] == 'test search'
    
    @pytest.mark.asyncio
    async def test_web_fetch_tool(self, agent):
        """Test web-fetch tool"""
        with patch('requests.get') as mock_get:
            mock_response = Mock()
            mock_response.text = '<html><head><title>Test Page</title></head><body><h1>Test Content</h1></body></html>'
            mock_response.status_code = 200
            mock_response.headers = {'content-type': 'text/html'}
            mock_response.url = 'https://example.com'
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response
            
            result = await agent.execute_tool_directly('web-fetch', {
                'url': 'https://example.com'
            })
            
            assert result['success']
            assert 'markdown' in result['data']

class TestTaskManagementTools:
    """Test all task management tools"""
    
    @pytest.mark.asyncio
    async def test_view_tasklist_tool(self, agent):
        """Test view_tasklist tool"""
        result = await agent.execute_tool_directly('view_tasklist', {})
        
        assert result['success']
        assert 'summary' in result['data']
    
    @pytest.mark.asyncio
    async def test_add_tasks_tool(self, agent):
        """Test add_tasks tool"""
        result = await agent.execute_tool_directly('add_tasks', {
            'name': 'Test Task',
            'description': 'This is a test task',
            'state': 'NOT_STARTED'
        })
        
        assert result['success']
        assert len(result['data']['created_tasks']) == 1
    
    @pytest.mark.asyncio
    async def test_update_tasks_tool(self, agent):
        """Test update_tasks tool"""
        # First add a task
        add_result = await agent.execute_tool_directly('add_tasks', {
            'name': 'Task to Update',
            'description': 'This task will be updated'
        })
        
        task_id = add_result['data']['created_tasks'][0]['task_id']
        
        # Now update it
        result = await agent.execute_tool_directly('update_tasks', {
            'task_id': task_id,
            'state': 'IN_PROGRESS'
        })
        
        assert result['success']
        assert len(result['data']['updated_tasks']) == 1

class TestMemoryTools:
    """Test all memory tools"""
    
    @pytest.mark.asyncio
    async def test_remember_tool(self, agent):
        """Test remember tool"""
        result = await agent.execute_tool_directly('remember', {
            'memory': 'This is an important piece of information to remember'
        })
        
        assert result['success']
        assert 'memory_id' in result['data']
    
    @pytest.mark.asyncio
    async def test_render_mermaid_tool(self, agent):
        """Test render-mermaid tool"""
        mermaid_code = '''graph TD
    A[Start] --> B[Process]
    B --> C[End]'''
        
        result = await agent.execute_tool_directly('render-mermaid', {
            'diagram_definition': mermaid_code,
            'title': 'Test Diagram'
        })
        
        assert result['success']
        assert 'html_content' in result['data']

class TestContentAnalysisTools:
    """Test all content analysis tools"""
    
    @pytest.mark.asyncio
    async def test_content_analysis_tools(self, agent):
        """Test content analysis tools with mock content"""
        # These tools require content references that would normally be created
        # by other tools when content is truncated. For testing, we'll verify
        # they handle missing references gracefully.
        
        # Test view-range-untruncated with non-existent reference
        result = await agent.execute_tool_directly('view-range-untruncated', {
            'reference_id': 'non_existent_ref',
            'start_line': 1,
            'end_line': 10
        })
        
        assert not result['success']  # Should fail gracefully
        assert 'not found' in result['error']
        
        # Test search-untruncated with non-existent reference
        result = await agent.execute_tool_directly('search-untruncated', {
            'reference_id': 'non_existent_ref',
            'search_term': 'test'
        })
        
        assert not result['success']  # Should fail gracefully
        assert 'not found' in result['error']

class TestAgentIntegration:
    """Test overall agent integration"""
    
    def test_all_tools_registered(self, agent):
        """Test that all expected tools are registered"""
        tools = agent.get_available_tools()
        tool_names = [tool['name'] for tool in tools]
        
        # File Management Tools (4)
        assert 'view' in tool_names
        assert 'str-replace-editor' in tool_names
        assert 'save-file' in tool_names
        assert 'remove-files' in tool_names
        
        # Code Intelligence Tools (2)
        assert 'codebase-retrieval' in tool_names
        assert 'diagnostics' in tool_names
        
        # Process Management Tools (6)
        assert 'launch-process' in tool_names
        assert 'read-process' in tool_names
        assert 'write-process' in tool_names
        assert 'kill-process' in tool_names
        assert 'list-processes' in tool_names
        assert 'read-terminal' in tool_names
        
        # Web Integration Tools (3)
        assert 'web-search' in tool_names
        assert 'web-fetch' in tool_names
        assert 'open-browser' in tool_names
        
        # Task Management Tools (4)
        assert 'view_tasklist' in tool_names
        assert 'add_tasks' in tool_names
        assert 'update_tasks' in tool_names
        assert 'reorganize_tasklist' in tool_names
        
        # Memory Tools (2)
        assert 'remember' in tool_names
        assert 'render-mermaid' in tool_names
        
        # Content Analysis Tools (2)
        assert 'view-range-untruncated' in tool_names
        assert 'search-untruncated' in tool_names
        
        # Total: 23 tools
        assert len(tool_names) >= 23
    
    def test_agent_status(self, agent):
        """Test agent status reporting"""
        status = agent.get_agent_status()
        
        assert status['status'] == 'active'
        assert status['tools_registered'] >= 23
        assert 'execution_stats' in status
        assert 'behavior_patterns' in status

if __name__ == '__main__':
    pytest.main([__file__, '-v'])
